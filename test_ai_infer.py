#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析功能
"""

import os
import sys
import grpc

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pb2 import analyzer_pb2 as analyzer_pb2
from pb2 import analyzer_pb2_grpc as analyzer_pb2_grpc

def test_ai_infer():
    """测试AI分析功能"""
    print("=== 测试AI分析功能 ===")
    
    with grpc.insecure_channel('localhost:50051') as channel:
        stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)
        
        # 创建一些测试轨迹点（模拟真实的家庭和工作模式）
        trajectory_points = [
            # 工作日白天在工作地点 (39.9042, 116.4074)
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672531200),  # 2023-01-01 09:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672534800),  # 2023-01-01 10:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672538400),  # 2023-01-01 11:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672542000),  # 2023-01-01 12:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672545600),  # 2023-01-01 13:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672549200),  # 2023-01-01 14:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672552800),  # 2023-01-01 15:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672556400),  # 2023-01-01 16:00
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672560000),  # 2023-01-01 17:00
            
            # 晚上在家 (39.9842, 116.3064)
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672574400),  # 2023-01-01 21:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672578000),  # 2023-01-01 22:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672581600),  # 2023-01-01 23:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672585200),  # 2023-01-02 00:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672588800),  # 2023-01-02 01:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672610400),  # 2023-01-02 07:00
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672614000),  # 2023-01-02 08:00
        ]
        
        request = analyzer_pb2.LocationInferRequest(
            points=trajectory_points,
            csv="",  # 不使用CSV文件
            ai=True  # 使用AI分析
        )
        
        print("发送AI分析请求...")
        print(f"轨迹点数量: {len(request.points)}")
        print(f"使用AI: {request.ai}")
        
        try:
            response = stub.Infer(request)
            
            print("\n=== AI分析结果 ===")
            print(f"家庭地址数量: {len(response.home)}")
            for i, home in enumerate(response.home):
                print(f"家庭地址 {i+1}:")
                print(f"  坐标: ({home.lat}, {home.lng})")
                print(f"  置信度: {home.confidence}")
                print(f"  地址: {home.location.suburb}")
            
            print(f"\n工作地址数量: {len(response.work)}")
            for i, work in enumerate(response.work):
                print(f"工作地址 {i+1}:")
                print(f"  坐标: ({work.lat}, {work.lng})")
                print(f"  置信度: {work.confidence}")
                print(f"  地址: {work.location.suburb}")
            
            print(f"\n其他重要地址数量: {len(response.another)}")
            print(f"头像大小: {len(response.portrait)} 字节")
            
        except grpc.RpcError as e:
            print(f"RPC错误: {e.code()} - {e.details()}")
        except Exception as e:
            print(f"其他错误: {e}")

if __name__ == '__main__':
    test_ai_infer()
