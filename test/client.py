import os
import sys
import grpc

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pb2 import analyzer_pb2 as analyzer_pb2
from pb2 import analyzer_pb2_grpc as analyzer_pb2_grpc

def run():
    # NOTE(gRPC Python Team): .close() is possible on a channel and should be
    # used in circumstances in which the channel is not going to be used
    # again. For example, channel.close()
    with grpc.insecure_channel('localhost:50051') as channel:
        stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)
        
        # Create a dummy request
        trajectory_points = [
            analyzer_pb2.TrajectoryPoint(lat=38.0522, lng=-118.2437, ts_sec=1672531200), # LA
            analyzer_pb2.TrajectoryPoint(lat=38.7749, lng=-122.4194, ts_sec=1672617600)  # SF
        ]
        
        request = analyzer_pb2.LocationInferRequest(
            points=trajectory_points,
            csv="/home/<USER>/Documents/zorel/sources/cs-analyzer/test_trajectory.csv",
            ai=False
        )
        
        print("-------------- Client Request --------------")
        print(request)
        
        # Make the call
        response = stub.Infer(request)
        
        print("-------------- Server Response --------------")

        print("Home Address size:", len(response.home))
        print("Home Address:", response.home)

        print("Work Address:", response.work)
        print("Another Address:", response.another)
        print("Portrait size (bytes):", len(response.portrait))
        
        print("\n-------------- Testing GeoCode --------------")
        # Test GeoCode method
        geo_request = analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672531200)
        geo_response = stub.GeoCode(geo_request)
        print("GeoCode Response:", geo_response)
        
        print("\n-------------- Testing MapMark --------------")
        # Test MapMark method
        map_request = analyzer_pb2.MapMarkRequest(points=trajectory_points)
        map_response = stub.MapMark(map_request)
        print("MapMark Response image size (bytes):", len(map_response.image))
        
        print("\n-------------- Testing Simplify --------------")
        # Test Simplify method (繁体转简体)
        simplify_request = analyzer_pb2.TextRequest(text="繁體中文測試文本")
        simplify_response = stub.Simplify(simplify_request)
        print("Simplify Response:", simplify_response.text)
        
        print("\n-------------- Testing Traditionalize --------------")
        # Test Traditionalize method (简体转繁体)
        traditional_request = analyzer_pb2.TextRequest(text="简体中文测试文本")
        traditional_response = stub.Traditionalize(traditional_request)
        print("Traditionalize Response:", traditional_response.text)

def Simplify():
    try:
        with grpc.insecure_channel('localhost:50051') as channel:
            stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)

            request = analyzer_pb2.TextRequest(text="繁體中文測試文本")
            response = stub.Simplify(request)
            print("Simplify Response:", response.text)
    except grpc.RpcError as e:
        print(f"Simplify RPC failed: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"Simplify failed: {e}")
def Traditionalize():
    try:
        with grpc.insecure_channel('localhost:50051') as channel:
            stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)

            request = analyzer_pb2.TextRequest(text="简体中文测试文本")
            response = stub.Traditionalize(request)
            print("Traditionalize Response:", response.text)
    except grpc.RpcError as e:
        print(f"Traditionalize RPC failed: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"Traditionalize failed: {e}")
def GeoCode():
    try:
        with grpc.insecure_channel('localhost:50051') as channel:
            stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)

            request = analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672531200)
            response = stub.GeoCode(request)
            print("GeoCode Response:", response)
    except grpc.RpcError as e:
        print(f"GeoCode RPC failed: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"GeoCode failed: {e}")

if __name__ == '__main__':
    run()
    Simplify()
    Traditionalize()
    GeoCode()

