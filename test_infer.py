#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Infer功能
"""

import os
import sys
import grpc

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pb2 import analyzer_pb2 as analyzer_pb2
from pb2 import analyzer_pb2_grpc as analyzer_pb2_grpc

def test_infer_with_csv():
    """测试使用CSV文件的Infer功能"""
    print("=== 测试Infer功能（使用CSV文件）===")
    
    with grpc.insecure_channel('localhost:50051') as channel:
        stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)
        
        # 创建请求（主要使用CSV文件）
        request = analyzer_pb2.LocationInferRequest(
            points=[],  # 空的轨迹点，主要使用CSV
            csv="test_trajectory_extended.csv",
            ai=True  # 使用AI分析器测试
        )
        
        print("发送请求...")
        print(f"CSV文件: {request.csv}")
        print(f"使用AI: {request.ai}")
        
        try:
            response = stub.Infer(request)
            
            print("\n=== 分析结果 ===")
            print(f"家庭地址数量: {len(response.home)}")
            for i, home in enumerate(response.home):
                print(f"家庭地址 {i+1}:")
                print(f"  坐标: ({home.lat}, {home.lng})")
                print(f"  置信度: {home.confidence}")
                print(f"  地址: {home.location.country} {home.location.state} {home.location.city}")
                print(f"       {home.location.county} {home.location.suburb} {home.location.road}")
            
            print(f"\n工作地址数量: {len(response.work)}")
            for i, work in enumerate(response.work):
                print(f"工作地址 {i+1}:")
                print(f"  坐标: ({work.lat}, {work.lng})")
                print(f"  置信度: {work.confidence}")
                print(f"  地址: {work.location.country} {work.location.state} {work.location.city}")
                print(f"       {work.location.county} {work.location.suburb} {work.location.road}")
            
            print(f"\n其他重要地址数量: {len(response.another)}")
            for i, other in enumerate(response.another):
                print(f"其他地址 {i+1}:")
                print(f"  坐标: ({other.lat}, {other.lng})")
                print(f"  置信度: {other.confidence}")
                print(f"  地址: {other.location.country} {other.location.state} {other.location.city}")
                print(f"       {other.location.county} {other.location.suburb} {other.location.road}")
            
            print(f"\n头像大小: {len(response.portrait)} 字节")
            
        except grpc.RpcError as e:
            print(f"RPC错误: {e.code()} - {e.details()}")
        except Exception as e:
            print(f"其他错误: {e}")

def test_infer_with_points():
    """测试使用轨迹点的Infer功能"""
    print("\n=== 测试Infer功能（使用轨迹点）===")
    
    with grpc.insecure_channel('localhost:50051') as channel:
        stub = analyzer_pb2_grpc.LocationInferServiceStub(channel)
        
        # 创建一些测试轨迹点
        trajectory_points = [
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672531200),  # 工作地点
            analyzer_pb2.TrajectoryPoint(lat=39.9042, lng=116.4074, ts_sec=1672534800),  # 工作地点
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672617600),  # 家庭地点
            analyzer_pb2.TrajectoryPoint(lat=39.9842, lng=116.3064, ts_sec=1672621200),  # 家庭地点
        ]
        
        request = analyzer_pb2.LocationInferRequest(
            points=trajectory_points,
            csv="",  # 不使用CSV文件
            ai=False  # 使用增强分析器
        )
        
        print("发送请求...")
        print(f"轨迹点数量: {len(request.points)}")
        print(f"使用AI: {request.ai}")
        
        try:
            response = stub.Infer(request)
            
            print("\n=== 分析结果 ===")
            print(f"家庭地址数量: {len(response.home)}")
            print(f"工作地址数量: {len(response.work)}")
            print(f"其他重要地址数量: {len(response.another)}")
            print(f"头像大小: {len(response.portrait)} 字节")
            
        except grpc.RpcError as e:
            print(f"RPC错误: {e.code()} - {e.details()}")
        except Exception as e:
            print(f"其他错误: {e}")

if __name__ == '__main__':
    test_infer_with_csv()
    test_infer_with_points()
