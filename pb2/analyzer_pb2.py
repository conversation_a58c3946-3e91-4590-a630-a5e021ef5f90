# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: analyzer.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'analyzer.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0e\x61nalyzer.proto\x12\x0fgeo.locinfer.v1\"a\n\x14LocationInferRequest\x12\x30\n\x06points\x18\x01 \x03(\x0b\x32 .geo.locinfer.v1.TrajectoryPoint\x12\x0b\n\x03\x63sv\x18\x02 \x01(\t\x12\n\n\x02\x61i\x18\x03 \x01(\x08\"B\n\x0eMapMarkRequest\x12\x30\n\x06points\x18\x01 \x03(\x0b\x32 .geo.locinfer.v1.TrajectoryPoint\" \n\x0fMapMarkResponse\x12\r\n\x05image\x18\x01 \x01(\x0c\"\x1b\n\x0bTextRequest\x12\x0c\n\x04text\x18\x01 \x01(\t\"\x1c\n\x0cTextResponse\x12\x0c\n\x04text\x18\x01 \x01(\t\";\n\x0fTrajectoryPoint\x12\x0b\n\x03lat\x18\x01 \x01(\x01\x12\x0b\n\x03lng\x18\x02 \x01(\x01\x12\x0e\n\x06ts_sec\x18\x03 \x01(\x03\"\xa4\x01\n\x15LocationInferResponse\x12&\n\x04home\x18\x01 \x03(\x0b\x32\x18.geo.locinfer.v1.Address\x12&\n\x04work\x18\x02 \x03(\x0b\x32\x18.geo.locinfer.v1.Address\x12)\n\x07\x61nother\x18\x03 \x03(\x0b\x32\x18.geo.locinfer.v1.Address\x12\x10\n\x08portrait\x18\x04 \x01(\x0c\"d\n\x07\x41\x64\x64ress\x12+\n\x08location\x18\x01 \x01(\x0b\x32\x19.geo.locinfer.v1.Location\x12\x0b\n\x03lat\x18\x02 \x01(\x01\x12\x0b\n\x03lng\x18\x03 \x01(\x01\x12\x12\n\nconfidence\x18\x04 \x01(\x02\"\xad\x01\n\x08Location\x12\x0f\n\x07\x63ountry\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\t\x12\x0c\n\x04\x63ity\x18\x03 \x01(\t\x12\x0e\n\x06\x63ounty\x18\x04 \x01(\t\x12\x0c\n\x04town\x18\x05 \x01(\t\x12\x0f\n\x07village\x18\x06 \x01(\t\x12\x0e\n\x06suburb\x18\x07 \x01(\t\x12\x0c\n\x04road\x18\x08 \x01(\t\x12\x14\n\x0chouse_number\x18\t \x01(\t\x12\x10\n\x08postcode\x18\n \x01(\t2\x9c\x03\n\x14LocationInferService\x12V\n\x05Infer\x12%.geo.locinfer.v1.LocationInferRequest\x1a&.geo.locinfer.v1.LocationInferResponse\x12\x46\n\x07GeoCode\x12 .geo.locinfer.v1.TrajectoryPoint\x1a\x19.geo.locinfer.v1.Location\x12L\n\x07MapMark\x12\x1f.geo.locinfer.v1.MapMarkRequest\x1a .geo.locinfer.v1.MapMarkResponse\x12G\n\x08Simplify\x12\x1c.geo.locinfer.v1.TextRequest\x1a\x1d.geo.locinfer.v1.TextResponse\x12M\n\x0eTraditionalize\x12\x1c.geo.locinfer.v1.TextRequest\x1a\x1d.geo.locinfer.v1.TextResponseB!Z\x1fpb/location_infer/v1;locationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'analyzer_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\037pb/location_infer/v1;locationpb'
  _globals['_LOCATIONINFERREQUEST']._serialized_start=35
  _globals['_LOCATIONINFERREQUEST']._serialized_end=132
  _globals['_MAPMARKREQUEST']._serialized_start=134
  _globals['_MAPMARKREQUEST']._serialized_end=200
  _globals['_MAPMARKRESPONSE']._serialized_start=202
  _globals['_MAPMARKRESPONSE']._serialized_end=234
  _globals['_TEXTREQUEST']._serialized_start=236
  _globals['_TEXTREQUEST']._serialized_end=263
  _globals['_TEXTRESPONSE']._serialized_start=265
  _globals['_TEXTRESPONSE']._serialized_end=293
  _globals['_TRAJECTORYPOINT']._serialized_start=295
  _globals['_TRAJECTORYPOINT']._serialized_end=354
  _globals['_LOCATIONINFERRESPONSE']._serialized_start=357
  _globals['_LOCATIONINFERRESPONSE']._serialized_end=521
  _globals['_ADDRESS']._serialized_start=523
  _globals['_ADDRESS']._serialized_end=623
  _globals['_LOCATION']._serialized_start=626
  _globals['_LOCATION']._serialized_end=799
  _globals['_LOCATIONINFERSERVICE']._serialized_start=802
  _globals['_LOCATIONINFERSERVICE']._serialized_end=1214
# @@protoc_insertion_point(module_scope)
