# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import analyzer_pb2 as analyzer__pb2

GRPC_GENERATED_VERSION = '1.74.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in analyzer_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LocationInferServiceStub(object):
    """✔ Python
    1. protoc ‑-python_out 指定输出即可  
    2. protoc ‑-grpc_python_out 生成 grpc stub  
    (无直接 option python_package, 目录用 proto 中 package 推导)

    其余主流语言可选
    option java_package = "geo.locinfer.v1";
    option csharp_namespace = "Geo.LocInfer.V1";
    =========================================

    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Infer = channel.unary_unary(
                '/geo.locinfer.v1.LocationInferService/Infer',
                request_serializer=analyzer__pb2.LocationInferRequest.SerializeToString,
                response_deserializer=analyzer__pb2.LocationInferResponse.FromString,
                _registered_method=True)
        self.GeoCode = channel.unary_unary(
                '/geo.locinfer.v1.LocationInferService/GeoCode',
                request_serializer=analyzer__pb2.TrajectoryPoint.SerializeToString,
                response_deserializer=analyzer__pb2.Location.FromString,
                _registered_method=True)
        self.MapMark = channel.unary_unary(
                '/geo.locinfer.v1.LocationInferService/MapMark',
                request_serializer=analyzer__pb2.MapMarkRequest.SerializeToString,
                response_deserializer=analyzer__pb2.MapMarkResponse.FromString,
                _registered_method=True)
        self.Simplify = channel.unary_unary(
                '/geo.locinfer.v1.LocationInferService/Simplify',
                request_serializer=analyzer__pb2.TextRequest.SerializeToString,
                response_deserializer=analyzer__pb2.TextResponse.FromString,
                _registered_method=True)
        self.Traditionalize = channel.unary_unary(
                '/geo.locinfer.v1.LocationInferService/Traditionalize',
                request_serializer=analyzer__pb2.TextRequest.SerializeToString,
                response_deserializer=analyzer__pb2.TextResponse.FromString,
                _registered_method=True)


class LocationInferServiceServicer(object):
    """✔ Python
    1. protoc ‑-python_out 指定输出即可  
    2. protoc ‑-grpc_python_out 生成 grpc stub  
    (无直接 option python_package, 目录用 proto 中 package 推导)

    其余主流语言可选
    option java_package = "geo.locinfer.v1";
    option csharp_namespace = "Geo.LocInfer.V1";
    =========================================

    """

    def Infer(self, request, context):
        """上传轨迹点 → 回传家庭地址 + 工作单位 + 头像
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GeoCode(self, request, context):
        """经纬度转地址
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MapMark(self, request, context):
        """经纬度点地图标注
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Simplify(self, request, context):
        """繁体转简体
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Traditionalize(self, request, context):
        """简体转繁体
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LocationInferServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Infer': grpc.unary_unary_rpc_method_handler(
                    servicer.Infer,
                    request_deserializer=analyzer__pb2.LocationInferRequest.FromString,
                    response_serializer=analyzer__pb2.LocationInferResponse.SerializeToString,
            ),
            'GeoCode': grpc.unary_unary_rpc_method_handler(
                    servicer.GeoCode,
                    request_deserializer=analyzer__pb2.TrajectoryPoint.FromString,
                    response_serializer=analyzer__pb2.Location.SerializeToString,
            ),
            'MapMark': grpc.unary_unary_rpc_method_handler(
                    servicer.MapMark,
                    request_deserializer=analyzer__pb2.MapMarkRequest.FromString,
                    response_serializer=analyzer__pb2.MapMarkResponse.SerializeToString,
            ),
            'Simplify': grpc.unary_unary_rpc_method_handler(
                    servicer.Simplify,
                    request_deserializer=analyzer__pb2.TextRequest.FromString,
                    response_serializer=analyzer__pb2.TextResponse.SerializeToString,
            ),
            'Traditionalize': grpc.unary_unary_rpc_method_handler(
                    servicer.Traditionalize,
                    request_deserializer=analyzer__pb2.TextRequest.FromString,
                    response_serializer=analyzer__pb2.TextResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'geo.locinfer.v1.LocationInferService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('geo.locinfer.v1.LocationInferService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LocationInferService(object):
    """✔ Python
    1. protoc ‑-python_out 指定输出即可  
    2. protoc ‑-grpc_python_out 生成 grpc stub  
    (无直接 option python_package, 目录用 proto 中 package 推导)

    其余主流语言可选
    option java_package = "geo.locinfer.v1";
    option csharp_namespace = "Geo.LocInfer.V1";
    =========================================

    """

    @staticmethod
    def Infer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/geo.locinfer.v1.LocationInferService/Infer',
            analyzer__pb2.LocationInferRequest.SerializeToString,
            analyzer__pb2.LocationInferResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GeoCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/geo.locinfer.v1.LocationInferService/GeoCode',
            analyzer__pb2.TrajectoryPoint.SerializeToString,
            analyzer__pb2.Location.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MapMark(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/geo.locinfer.v1.LocationInferService/MapMark',
            analyzer__pb2.MapMarkRequest.SerializeToString,
            analyzer__pb2.MapMarkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Simplify(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/geo.locinfer.v1.LocationInferService/Simplify',
            analyzer__pb2.TextRequest.SerializeToString,
            analyzer__pb2.TextResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Traditionalize(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/geo.locinfer.v1.LocationInferService/Traditionalize',
            analyzer__pb2.TextRequest.SerializeToString,
            analyzer__pb2.TextResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
