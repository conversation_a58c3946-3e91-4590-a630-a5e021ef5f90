// location_infer_v1.proto
syntax = "proto3";

package geo.locinfer.v1;

// ================= 语言选项 =================
// ✔ Go
option go_package = "pb/location_infer/v1;locationpb";

// ✔ Python
// 1. protoc ‑-python_out 指定输出即可  
// 2. protoc ‑-grpc_python_out 生成 grpc stub  
// (无直接 option python_package, 目录用 proto 中 package 推导)

// 其余主流语言可选
// option java_package = "geo.locinfer.v1";
// option csharp_namespace = "Geo.LocInfer.V1";
// =========================================

service LocationInferService {
  // 上传轨迹点 → 回传家庭地址 + 工作单位 + 头像
  rpc Infer(LocationInferRequest) returns (LocationInferResponse);
  // 经纬度转地址
  rpc GeoCode(TrajectoryPoint) returns (Location);
  // 经纬度点地图标注
  rpc MapMark(MapMarkRequest) returns (MapMarkResponse);
  // 繁体转简体
  rpc Simplify(TextRequest) returns (TextResponse);
  // 简体转繁体
  rpc Traditionalize(TextRequest) returns (TextResponse);
}

/* ================== 请求 ================== */
message LocationInferRequest {
  repeated TrajectoryPoint points      = 1; // 轨迹点
  string                   csv         = 2; // csv文件路径
  bool                     ai          = 3; // 是否要求AI分析
}

message MapMarkRequest {
  repeated TrajectoryPoint points      = 1; // 轨迹点
}

message MapMarkResponse {
  bytes image = 1; // 图像 (jpg/png 或 gzip-jpg/png)
}

message TextRequest {
  string text = 1; // 输入文本
}

message TextResponse {
  string text = 1; // 输出文本
}

message TrajectoryPoint {
  double lat    = 1; // 纬度
  double lng    = 2; // 经度
  int64  ts_sec = 3; // UTC时间戳秒
}

/* ================== 响应 ================== */
message LocationInferResponse {
  repeated Address home       = 1; // 家庭地址
  repeated Address work       = 2; // 工作地址
  repeated Address another    = 3;
  bytes   portrait   = 4; // 图像 (jpg/png 或 gzip-jpg/png)
}

message Address {
  Location location   = 1; // 地址文本
  double   lat        = 2; // 纬度
  double   lng        = 3; // 经度
  float    confidence = 4; // 0–1,置信度
}

message Location {
  string country = 1; // 国家
  string state   = 2; // 省/州
  string city    = 3; // 市
  string county  = 4; // 区/县
  string town    = 5; // 镇
  string village = 6; // 村
  string suburb  = 7; // 街道
  string road    = 8; // 路
  string house_number = 9; // 门牌号
  string postcode = 10; // 邮编
}

