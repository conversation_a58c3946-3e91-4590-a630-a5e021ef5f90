from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any

@dataclass
class LocationInfo:
    """地理位置信息数据类"""
    latitude: float
    longitude: float
    formatted_address: str
    country: str = ""
    administrative_area_level_1: str = ""  # 省/州
    administrative_area_level_2: str = ""  # 市
    administrative_area_level_3: str = ""  # 区/县
    locality: str = ""  # 城市
    village: str = ""  # 村    
    sublocality: str = ""  # 街道/社区
    neighborhood: str = ""  # 小区
    route: str = ""  # 路名
    street_number: str = ""  # 门牌号
    amenity: str = ""  # 附属设施
    historic: str = "" # 历史遗迹
    postal_code: str = ""  # 邮编
    place_types: List[str] = None
    
    def __post_init__(self):
        if self.place_types is None:
            self.place_types = []