#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版地理位置数据分析工具
集成Google Maps API地理位置反查功能
支持方法调用形式的完整分析流程
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
import pandas as pd
import numpy as np
import hdbscan
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import json
import logging
import requests
import time
import pickle
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config, DEFAULT_CONFIG


@dataclass
class LocationInfo:
    """地理位置信息数据类"""
    latitude: float
    longitude: float
    formatted_address: str
    country: str = ""
    administrative_area_level_1: str = ""  # 省/州
    administrative_area_level_2: str = ""  # 市
    administrative_area_level_3: str = ""  # 区/县
    locality: str = ""  # 城市
    sublocality: str = ""  # 街道/社区
    route: str = ""  # 路名
    street_number: str = ""  # 门牌号
    postal_code: str = ""  # 邮编
    place_types: List[str] = None

    def __post_init__(self):
        if self.place_types is None:
            self.place_types = []


class GeocodingService:
    """地理位置反查服务类"""

    def __init__(self, api_key: str,
                 cache_file: str = "geocoding_cache.pkl",
                 language: str = "zh-CN"):
        """
        初始化地理位置反查服务

        Args:
            api_key: Google Maps API密钥
            cache_file: 缓存文件路径
            language: 返回结果的语言
        """
        self.api_key = api_key
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.cache_file = Path(cache_file)
        self.language = language
        self.request_count = 0
        self.max_requests_per_second = 10  # API限制

        # 设置日志（必须在_load_cache之前）
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        # 加载缓存（在logger初始化之后）
        self.cache = self._load_cache()

    def _load_cache(self) -> Dict[str, LocationInfo]:
        """加载缓存数据"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info(f"已加载 {len(cache)} 条缓存记录")
                return cache
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
        return {}

    def _save_cache(self) -> None:
        """保存缓存数据"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
            self.logger.info(f"已保存 {len(self.cache)} 条缓存记录")
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")

    def _get_cache_key(self, latitude: float, longitude: float, precision: int = 4) -> str:
        """生成缓存键，使用指定精度避免重复查询相近位置"""
        lat_rounded = round(latitude, precision)
        lon_rounded = round(longitude, precision)
        return f"{lat_rounded},{lon_rounded}"

    def _rate_limit(self) -> None:
        """API请求频率限制"""
        if self.request_count >= self.max_requests_per_second:
            time.sleep(1)
            self.request_count = 0

    def reverse_geocode(self, latitude: float, longitude: float,
                       use_cache: bool = True) -> Optional[LocationInfo]:
        """
        反向地理编码：通过经纬度获取地理位置信息

        Args:
            latitude: 纬度
            longitude: 经度
            use_cache: 是否使用缓存

        Returns:
            LocationInfo: 地理位置信息，失败时返回None
        """
        # 检查缓存
        cache_key = self._get_cache_key(latitude, longitude)
        if use_cache and cache_key in self.cache:
            self.logger.debug(f"从缓存获取位置信息: {cache_key}")
            return self.cache[cache_key]

        # 检查API密钥
        if self.api_key == "YOUR_GOOGLE_MAPS_API_KEY":
            self.logger.error("请设置有效的Google Maps API密钥")
            return None

        # 频率限制
        self._rate_limit()

        # 构建请求参数
        params = {
            'latlng': f"{latitude},{longitude}",
            'key': self.api_key,
            'language': self.language,
            'result_type': 'street_address|route|neighborhood|locality|administrative_area_level_3|administrative_area_level_2|administrative_area_level_1|country'
        }

        try:
            self.logger.debug(f"请求地理位置信息: ({latitude}, {longitude})")
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()

            self.request_count += 1
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                location_info = self._parse_response(data['results'][0], latitude, longitude)

                # 保存到缓存
                if use_cache:
                    self.cache[cache_key] = location_info

                self.logger.info(f"成功获取位置信息: {location_info.formatted_address}")
                return location_info
            else:
                self.logger.warning(f"API返回错误: {data.get('status', 'UNKNOWN')}")
                return None

        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"反向地理编码失败: {e}")
            return None

    def _parse_response(self, result: Dict[str, Any], latitude: float, longitude: float) -> LocationInfo:
        """解析Google Maps API响应"""
        location_info = LocationInfo(
            latitude=latitude,
            longitude=longitude,
            formatted_address=result.get('formatted_address', ''),
            place_types=result.get('types', [])
        )

        # 解析地址组件
        for component in result.get('address_components', []):
            types = component.get('types', [])
            long_name = component.get('long_name', '')

            if 'country' in types:
                location_info.country = long_name
            elif 'administrative_area_level_1' in types:
                location_info.administrative_area_level_1 = long_name
            elif 'administrative_area_level_2' in types:
                location_info.administrative_area_level_2 = long_name
            elif 'administrative_area_level_3' in types:
                location_info.administrative_area_level_3 = long_name
            elif 'locality' in types:
                location_info.locality = long_name
            elif 'sublocality' in types or 'sublocality_level_1' in types:
                location_info.sublocality = long_name
            elif 'route' in types:
                location_info.route = long_name
            elif 'street_number' in types:
                location_info.street_number = long_name
            elif 'postal_code' in types:
                location_info.postal_code = long_name

        return location_info

    def get_location_summary(self, location_info: LocationInfo) -> str:
        """获取位置信息摘要"""
        if not location_info:
            return "未知位置"

        # 构建层次化地址
        parts = []

        if location_info.street_number and location_info.route:
            parts.append(f"{location_info.route}{location_info.street_number}")
        elif location_info.route:
            parts.append(location_info.route)

        if location_info.sublocality:
            parts.append(location_info.sublocality)

        if location_info.administrative_area_level_3:
            parts.append(location_info.administrative_area_level_3)

        if location_info.administrative_area_level_2:
            parts.append(location_info.administrative_area_level_2)

        if location_info.administrative_area_level_1:
            parts.append(location_info.administrative_area_level_1)

        return ", ".join(parts) if parts else location_info.formatted_address

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.cache),
            'cache_file': str(self.cache_file),
            'cache_file_exists': self.cache_file.exists(),
            'request_count': self.request_count
        }


class LocationAnalyzer:
    """增强版地理位置数据分析器

    集成Google Maps API地理位置反查功能
    用于分析用户移动轨迹，识别家庭住址、工作单位和其他重要聚集点
    """

    def __init__(self,
                 min_cluster_size: int = None,
                 cluster_selection_epsilon: Optional[float] = None,
                 google_api_key: str = None,
                 config: Optional[Config] = None):
        """
        初始化增强版位置分析器

        Args:
            min_cluster_size: HDBSCAN最小聚类大小（相当于原来的min_samples），如果为None则使用配置中的默认值
            cluster_selection_epsilon: 聚类选择阈值（米），可选，用于控制聚类的粒度
            google_api_key: Google Maps API密钥，如果为None则使用配置中的默认值
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or DEFAULT_CONFIG
        self.min_cluster_size = min_cluster_size or self.config.DEFAULT_MIN_CLUSTER_SIZE
        self.cluster_selection_epsilon = cluster_selection_epsilon or self.config.DEFAULT_CLUSTER_SELECTION_EPSILON
        self.min_samples = max(1, self.min_cluster_size // 2)  # 设置min_samples为min_cluster_size的一半
        self.data: Optional[pd.DataFrame] = None
        self.clusters: Optional[np.ndarray] = None
        self.cluster_centers: Optional[List[Dict]] = None
        self._scaler: Optional[StandardScaler] = None

        # 初始化地理位置反查服务
        self.geocoding_service = GeocodingService(
            api_key=google_api_key,
            cache_file="location_geocoding_cache.pkl"
        )

        # 配置日志
        self._setup_logging()
        self.logger.info("增强版位置分析器已初始化，支持地理位置反查")

    def _setup_logging(self) -> None:
        """设置日志配置"""
        logging.basicConfig(
            level=self.config.LOG_LEVEL,
            format=self.config.LOG_FORMAT,
            force=True
        )
        self.logger: logging.Logger = logging.getLogger(self.__class__.__name__)

    def load_data(self, file_path: str) -> bool:
        """加载位置数据

        Args:
            file_path: 数据文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info(f"开始加载数据文件: {file_path}")

            # 使用Path对象进行文件操作
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            if file_path_obj.stat().st_size == 0:
                raise ValueError("文件为空")

            # 读取数据
            self.data = pd.read_csv(file_path)
            self.logger.info(f"原始数据行数: {len(self.data)}")

            # 验证和清理数据
            if not self._validate_and_clean_data():
                return False

            # 数据预处理
            self._preprocess_data()

            # 数据质量检查
            self._check_data_quality()

            self.logger.info(f"成功加载 {len(self.data)} 条位置记录")
            self.logger.info(f"时间范围: {self.data['timestamp'].min()} 到 {self.data['timestamp'].max()}")
            return True

        except FileNotFoundError as e:
            self.logger.error(f"文件未找到: {e}")
            return False
        except ValueError as e:
            self.logger.error(f"数据验证错误: {e}")
            return False
        except pd.errors.EmptyDataError:
            self.logger.error("CSV文件为空或格式不正确")
            return False
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            return False

    def _validate_and_clean_data(self) -> bool:
        """验证和清理数据"""
        # 验证必要的列是否存在
        required_columns = ['timestamp', 'latitude', 'longitude']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")

        # 处理空值
        initial_count = len(self.data)
        null_counts = self.data[required_columns].isnull().sum()
        if null_counts.any():
            self.logger.warning(f"发现空值: {null_counts[null_counts > 0].to_dict()}")
            self.data = self.data.dropna(subset=required_columns)
            removed_count = initial_count - len(self.data)
            if removed_count > 0:
                self.logger.info(f"已删除 {removed_count} 条包含空值的记录")

        # 时间戳格式转换
        try:
            self.data['timestamp'] = pd.to_datetime(self.data['timestamp'])
        except Exception as e:
            raise ValueError(f"时间戳格式解析失败: {e}")

        # 验证坐标范围
        return self._validate_coordinates()

    def _validate_coordinates(self) -> bool:
        """验证坐标有效性"""
        # 检查坐标范围
        valid_coords = (
            self.data['latitude'].between(-90, 90) &
            self.data['longitude'].between(-180, 180)
        )

        invalid_count = (~valid_coords).sum()
        if invalid_count > 0:
            self.logger.warning(f"发现 {invalid_count} 条无效坐标记录")
            self.data = self.data[valid_coords]
            self.logger.info(f"已删除 {invalid_count} 条包含无效坐标的记录")

        # 检查数据是否为空
        if len(self.data) == 0:
            self.logger.error("清理后数据为空，请检查输入文件")
            return False

        return True

    def _preprocess_data(self) -> None:
        """数据预处理"""
        # 时间特征提取
        self.data['hour'] = self.data['timestamp'].dt.hour
        self.data['day_of_week'] = self.data['timestamp'].dt.dayofweek
        self.data['is_weekend'] = self.data['day_of_week'] >= 5
        self.data['date'] = self.data['timestamp'].dt.date

        # 按时间排序
        self.data = self.data.sort_values('timestamp').reset_index(drop=True)

    def _check_data_quality(self) -> None:
        """检查数据质量"""
        data_count = len(self.data)
        min_required = self.min_samples * 2

        if data_count < min_required:
            self.logger.warning(
                f"数据量较少 ({data_count} 条记录)，建议至少 {min_required} 条记录以获得更好的分析效果"
            )

        # 检查时间跨度
        time_span = (self.data['timestamp'].max() - self.data['timestamp'].min()).days
        if time_span < 7:
            self.logger.warning(f"数据时间跨度较短 ({time_span} 天)，可能影响地点识别准确性")

    def cluster_locations(self) -> Optional[List[Dict]]:
        """使用HDBSCAN聚类识别停留点

        Returns:
            Optional[List[Dict]]: 聚类中心列表，失败时返回None
        """
        if self.data is None:
            self.logger.error("请先加载数据")
            return None

        try:
            self.logger.info("开始进行位置聚类分析（使用HDBSCAN）")

            # 提取坐标
            coords = self.data[['latitude', 'longitude']].values

            # 将坐标转换为米制单位以提高聚类精度
            coords_metric = self._convert_to_metric_coords(coords)

            # HDBSCAN聚类
            # min_cluster_size: 最小聚类大小
            # min_samples: 核心点的最小邻居数，用于控制噪声敏感度
            # cluster_selection_epsilon: 可选的聚类选择阈值
            hdbscan_params = {
                'min_cluster_size': self.min_cluster_size,
                'min_samples': max(1, self.min_cluster_size // 2),  # 通常设为min_cluster_size的一半
                'metric': 'euclidean',
                'core_dist_n_jobs': -1
            }

            # 如果设置了cluster_selection_epsilon，则使用它
            if self.cluster_selection_epsilon is not None:
                hdbscan_params['cluster_selection_epsilon'] = self.cluster_selection_epsilon

            clusterer = hdbscan.HDBSCAN(**hdbscan_params)

            cluster_labels = clusterer.fit_predict(coords_metric)
            self.data['cluster'] = cluster_labels

            # 保存聚类器以便后续分析
            self.clusterer = clusterer

            # 计算聚类中心和统计信息
            self.cluster_centers = self._calculate_cluster_centers()

            # 记录聚类结果
            self._log_clustering_results()

            return self.cluster_centers

        except Exception as e:
            self.logger.error(f"聚类分析失败: {e}")
            return None

    def _convert_to_metric_coords(self, coords: np.ndarray) -> np.ndarray:
        """将地理坐标转换为米制坐标系

        Args:
            coords: 地理坐标数组 [[lat, lon], ...]

        Returns:
            np.ndarray: 米制坐标数组 [[x, y], ...]
        """
        # 使用简单的等距圆柱投影（适用于小范围区域）
        # 选择数据中心点作为投影原点
        center_lat = np.mean(coords[:, 0])
        center_lon = np.mean(coords[:, 1])

        # 地球半径（米）
        earth_radius = 6371000.0

        # 转换为弧度
        lat_rad = np.radians(coords[:, 0])
        lon_rad = np.radians(coords[:, 1])
        center_lat_rad = np.radians(center_lat)
        center_lon_rad = np.radians(center_lon)

        # 计算相对于中心点的米制坐标
        x = earth_radius * (lon_rad - center_lon_rad) * np.cos(center_lat_rad)
        y = earth_radius * (lat_rad - center_lat_rad)

        return np.column_stack([x, y])

    def _km_to_degrees(self, km: float, latitude: float) -> float:
        """将公里转换为度，考虑纬度影响

        Args:
            km: 距离（公里）
            latitude: 纬度

        Returns:
            float: 对应的度数
        """
        # 在给定纬度下，经度1度对应的公里数
        lat_rad = np.radians(latitude)
        km_per_degree_lon = self.config.EARTH_RADIUS_KM * np.cos(lat_rad)
        km_per_degree_lat = self.config.EARTH_RADIUS_KM

        # 使用平均值作为转换系数
        avg_km_per_degree = (km_per_degree_lat + km_per_degree_lon) / 2
        return km / avg_km_per_degree

    def _calculate_cluster_centers(self) -> List[Dict]:
        """计算聚类中心和统计信息"""
        cluster_centers = []
        unique_clusters = self.data['cluster'].unique()

        for cluster_id in unique_clusters:
            cluster_data = self.data[self.data['cluster'] == cluster_id]
            if len(cluster_data) == 0:
                continue

            center = {
                'cluster_id': int(cluster_id),
                'latitude': float(cluster_data['latitude'].mean()),
                'longitude': float(cluster_data['longitude'].mean()),
                'count': len(cluster_data),
                'first_visit': cluster_data['timestamp'].min(),
                'last_visit': cluster_data['timestamp'].max(),
                # 添加更多统计信息
                'std_lat': float(cluster_data['latitude'].std()),
                'std_lon': float(cluster_data['longitude'].std()),
                'unique_days': cluster_data['date'].nunique()
            }
            cluster_centers.append(center)

        # 按访问次数排序
        cluster_centers.sort(key=lambda x: x['count'], reverse=True)
        return cluster_centers

    def _log_clustering_results(self) -> None:
        """记录聚类结果统计信息"""
        if not self.cluster_centers:
            return

        valid_clusters = [c for c in self.cluster_centers if c['cluster_id'] != -1]
        noise_count = len(self.data[self.data['cluster'] == -1])

        self.logger.info(f"HDBSCAN识别出 {len(valid_clusters)} 个有效聚集点")
        self.logger.info(f"噪声点数量: {noise_count} ({noise_count/len(self.data)*100:.1f}%)")

        if valid_clusters:
            cluster_sizes = [c['count'] for c in valid_clusters]
            self.logger.info(f"聚类大小统计 - 最大: {max(cluster_sizes)}, "
                           f"最小: {min(cluster_sizes)}, 平均: {np.mean(cluster_sizes):.1f}")

            # 如果有聚类器，记录额外的HDBSCAN信息
            if hasattr(self, 'clusterer') and hasattr(self.clusterer, 'cluster_persistence_'):
                persistence = self.clusterer.cluster_persistence_
                if len(persistence) > 0:
                    self.logger.info(f"HDBSCAN聚类强度范围: "
                                   f"{persistence.min():.3f} - {persistence.max():.3f}")

    def analyze_cluster_patterns(self) -> Optional[List[Dict]]:
        """分析每个聚集点的访问模式

        Returns:
            Optional[List[Dict]]: 聚类分析结果，失败时返回None
        """
        if self.cluster_centers is None:
            self.logger.error("请先进行聚类分析")
            return None

        self.logger.info("开始分析聚集点访问模式")
        cluster_analysis: List[Dict] = []

        for center in self.cluster_centers:
            cluster_id = center['cluster_id']
            cluster_data = self.data[self.data['cluster'] == cluster_id]

            # 时间模式分析（向量化操作提高性能）
            time_patterns = self._analyze_time_patterns(cluster_data)

            # 停留时长分析
            stay_durations = self._calculate_stay_durations(cluster_data)
            avg_stay_duration = np.mean(stay_durations) if stay_durations else 0

            # 计算置信度分数
            confidence_score = self._calculate_confidence_score(time_patterns, len(cluster_data))

            # 计算访问频率（每天平均访问次数）
            days_span = max((center['last_visit'] - center['first_visit']).days + 1, 1)
            visit_frequency = center['count'] / days_span

            analysis: Dict[str, Any] = {
                'cluster_id': cluster_id,
                'coordinates': (center['latitude'], center['longitude']),
                'total_visits': center['count'],
                'unique_days': center.get('unique_days', 0),
                'time_patterns': time_patterns,
                'avg_stay_hours': round(avg_stay_duration, 2),
                'median_stay_hours': round(np.median(stay_durations), 2) if stay_durations else 0,
                'stay_durations_sample': [round(d, 2) for d in stay_durations[:10]],
                'visit_frequency_per_day': round(visit_frequency, 2),
                'confidence_score': round(confidence_score, 2),
                'first_visit': center['first_visit'],
                'last_visit': center['last_visit'],
                'location_stability': self._calculate_location_stability(center)
            }

            cluster_analysis.append(analysis)

        self.logger.info(f"完成 {len(cluster_analysis)} 个聚集点的模式分析")
        return cluster_analysis

    def _analyze_time_patterns(self, cluster_data: pd.DataFrame) -> Dict[str, int]:
        """分析时间模式（向量化操作）"""
        hours = cluster_data['hour']
        is_weekend = cluster_data['is_weekend']

        return {
            'morning_visits': int(((hours >= 6) & (hours < 12)).sum()),
            'afternoon_visits': int(((hours >= 12) & (hours < 18)).sum()),
            'evening_visits': int(((hours >= 18) & (hours < 24)).sum()),
            'night_visits': int(((hours >= 0) & (hours < 6)).sum()),
            'weekday_visits': int((~is_weekend).sum()),
            'weekend_visits': int(is_weekend.sum()),
        }

    def _calculate_location_stability(self, center: Dict) -> float:
        """计算位置稳定性分数

        Args:
            center: 聚类中心信息

        Returns:
            float: 稳定性分数 (0-1)
        """
        # 基于坐标标准差计算稳定性
        std_lat = center.get('std_lat', 0)
        std_lon = center.get('std_lon', 0)

        # 标准差越小，稳定性越高
        avg_std = (std_lat + std_lon) / 2
        stability = 1.0 / (1.0 + avg_std * 1000)  # 放大系数使分数更有区分度

        return min(max(stability, 0.0), 1.0)

    def _calculate_stay_durations(self, cluster_data: pd.DataFrame) -> List[float]:
        """计算停留时长，使用优化算法

        Args:
            cluster_data: 聚类数据

        Returns:
            List[float]: 停留时长列表（小时）
        """
        if len(cluster_data) == 0:
            return []

        # 按时间排序
        sorted_data = cluster_data.sort_values('timestamp').reset_index(drop=True)
        stay_durations: List[float] = []

        if len(sorted_data) == 1:
            return [self.config.MIN_STAY_HOURS]  # 单个记录默认最小停留时间

        # 计算相邻记录的时间间隔
        timestamps = sorted_data['timestamp'].values
        time_diffs = np.diff(timestamps) / np.timedelta64(1, 's')  # 转换为秒

        # 识别停留会话
        break_threshold = self.config.STAY_BREAK_MINUTES * 60  # 转换为秒
        session_breaks = time_diffs > break_threshold

        # 计算每个会话的停留时长
        session_start = 0
        for i, is_break in enumerate(session_breaks):
            if is_break:
                # 计算当前会话的停留时长
                session_end = i + 1
                if session_end > session_start:
                    # 修复：使用pandas Timestamp对象进行计算
                    start_time = pd.Timestamp(timestamps[session_start])
                    end_time = pd.Timestamp(timestamps[i])
                    duration_seconds = (end_time - start_time).total_seconds()
                    duration_hours = duration_seconds / 3600
                    if duration_hours >= self.config.MIN_STAY_HOURS:
                        stay_durations.append(duration_hours)
                session_start = i + 1

        # 处理最后一个会话
        if session_start < len(timestamps):
            start_time = pd.Timestamp(timestamps[session_start])
            end_time = pd.Timestamp(timestamps[-1])
            duration_seconds = (end_time - start_time).total_seconds()
            duration_hours = duration_seconds / 3600
            if duration_hours >= self.config.MIN_STAY_HOURS:
                stay_durations.append(duration_hours)

        return stay_durations

    def _calculate_confidence_score(self, time_patterns: Dict[str, int], total_visits: int) -> float:
        """计算地点识别置信度分数

        Args:
            time_patterns: 时间模式统计
            total_visits: 总访问次数

        Returns:
            float: 置信度分数 (0-1)
        """
        if total_visits == 0:
            return 0.0

        # 计算时间分布比例
        morning_ratio = time_patterns['morning_visits'] / total_visits
        afternoon_ratio = time_patterns['afternoon_visits'] / total_visits
        evening_ratio = time_patterns['evening_visits'] / total_visits
        night_ratio = time_patterns['night_visits'] / total_visits

        # 计算时间分布的熵（衡量分布的集中程度）
        time_distribution = [morning_ratio, afternoon_ratio, evening_ratio, night_ratio]
        time_entropy = -sum(p * np.log(p + 1e-10) for p in time_distribution if p > 0)

        # 归一化熵值（最大熵为log(4)）
        max_entropy = np.log(4)
        normalized_entropy = time_entropy / max_entropy

        # 置信度分数：熵越低（分布越集中），置信度越高
        confidence = 1.0 - normalized_entropy

        # 考虑访问次数的影响：访问次数越多，置信度越高
        visit_factor = min(total_visits / 50.0, 1.0)  # 50次访问达到满分

        final_confidence = confidence * 0.8 + visit_factor * 0.2
        return min(max(final_confidence, 0.0), 1.0)

    def identify_key_locations(self, cluster_analysis: List[Dict]) -> Dict[str, Any]:
        """识别关键地点类型

        Args:
            cluster_analysis: 聚类分析结果

        Returns:
            Dict[str, Any]: 关键地点识别结果
        """
        key_locations: Dict[str, Any] = {
            'home': None,
            'work': None,
            'other_important': []
        }

        if not cluster_analysis:
            self.logger.warning("没有聚类分析结果，无法识别关键地点")
            return key_locations

        self.logger.info("开始识别关键地点")

        # 过滤掉噪声点
        valid_clusters = [c for c in cluster_analysis if c['cluster_id'] != -1]

        if not valid_clusters:
            self.logger.warning("没有有效的聚类点")
            return key_locations

        # 按综合评分排序
        self._rank_clusters_by_importance(valid_clusters)

        # 识别各类地点
        for cluster in valid_clusters:
            time_patterns = cluster['time_patterns']
            total_visits = cluster['total_visits']
            avg_stay_hours = cluster['avg_stay_hours']

            # 计算各类地点的得分
            home_score = self._calculate_home_score(time_patterns, total_visits, avg_stay_hours)
            work_score = self._calculate_work_score(time_patterns, total_visits, avg_stay_hours)

            # 添加得分到聚类信息中
            cluster['home_score'] = home_score
            cluster['work_score'] = work_score

            # 识别家庭住址
            if (home_score > self.config.HOME_SCORE_THRESHOLD and
                (key_locations['home'] is None or home_score > key_locations['home']['home_score'])):
                key_locations['home'] = cluster
                self.logger.info(f"识别到家庭住址候选: 聚类{cluster['cluster_id']}, 得分: {home_score:.2f}")

            # 识别工作地点
            elif (work_score > self.config.WORK_SCORE_THRESHOLD and
                  (key_locations['work'] is None or work_score > key_locations['work']['work_score'])):
                key_locations['work'] = cluster
                self.logger.info(f"识别到工作地点候选: 聚类{cluster['cluster_id']}, 得分: {work_score:.2f}")

            # 识别其他重要地点
            elif (total_visits > self.config.MIN_OTHER_VISITS and
                  cluster.get('confidence_score', 0) > self.config.OTHER_IMPORTANT_THRESHOLD):
                key_locations['other_important'].append(cluster)

        # 限制其他重要地点的数量
        key_locations['other_important'] = key_locations['other_important'][:self.config.MAX_OTHER_LOCATIONS]

        self._log_identification_results(key_locations)
        return key_locations

    def _rank_clusters_by_importance(self, clusters: List[Dict]) -> None:
        """按重要性对聚类进行排序"""
        def importance_score(cluster: Dict) -> float:
            confidence = cluster.get('confidence_score', 0)
            visit_frequency = cluster.get('visit_frequency_per_day', 0)
            unique_days = cluster.get('unique_days', 0)
            stability = cluster.get('location_stability', 0)

            # 综合评分：置信度40% + 访问频率30% + 访问天数20% + 位置稳定性10%
            return (confidence * 0.4 +
                   min(visit_frequency / 5.0, 1.0) * 0.3 +  # 每天5次访问为满分
                   min(unique_days / 30.0, 1.0) * 0.2 +     # 30天为满分
                   stability * 0.1)

        clusters.sort(key=importance_score, reverse=True)

    def _log_identification_results(self, key_locations: Dict[str, Any]) -> None:
        """记录地点识别结果"""
        if key_locations['home']:
            home = key_locations['home']
            self.logger.info(f"识别到家庭住址: 坐标{home['coordinates']}, "
                           f"访问{home['total_visits']}次, 得分{home['home_score']:.2f}")

        if key_locations['work']:
            work = key_locations['work']
            self.logger.info(f"识别到工作地点: 坐标{work['coordinates']}, "
                           f"访问{work['total_visits']}次, 得分{work['work_score']:.2f}")

        other_count = len(key_locations['other_important'])
        if other_count > 0:
            self.logger.info(f"识别到{other_count}个其他重要地点")

    def _calculate_home_score(self, time_patterns: Dict[str, int], total_visits: int, avg_stay_hours: float) -> float:
        """计算家庭地点得分

        Args:
            time_patterns: 时间模式统计
            total_visits: 总访问次数
            avg_stay_hours: 平均停留时间（小时）

        Returns:
            float: 家庭地点得分 (0-1)
        """
        if total_visits == 0:
            return 0.0

        # 计算各时间段的访问比例
        night_ratio = time_patterns['night_visits'] / total_visits
        evening_ratio = time_patterns['evening_visits'] / total_visits
        weekend_ratio = time_patterns['weekend_visits'] / total_visits

        # 家庭特征权重计算
        # 1. 夜间访问比例（权重40%）- 家里通常夜间停留
        night_score = night_ratio

        # 2. 晚间访问比例（权重30%）- 下班后回家
        evening_score = evening_ratio

        # 3. 周末访问比例（权重20%）- 周末在家时间多
        weekend_score = weekend_ratio

        # 4. 停留时长评分（权重10%）- 家里停留时间长
        # 理想停留时间8-12小时，超过12小时不再加分
        stay_score = min(avg_stay_hours / 10.0, 1.0) if avg_stay_hours > 2 else 0

        # 综合评分
        home_score = (night_score * 0.4 +
                     evening_score * 0.3 +
                     weekend_score * 0.2 +
                     stay_score * 0.1)

        # 访问频率加成：如果访问次数很多，适当提高得分
        frequency_bonus = min(total_visits / 100.0, 0.2)  # 最多20%加成

        final_score = home_score + frequency_bonus
        return min(max(final_score, 0.0), 1.0)

    def _calculate_work_score(self, time_patterns: Dict[str, int], total_visits: int, avg_stay_hours: float) -> float:
        """计算工作地点得分

        Args:
            time_patterns: 时间模式统计
            total_visits: 总访问次数
            avg_stay_hours: 平均停留时间（小时）

        Returns:
            float: 工作地点得分 (0-1)
        """
        if total_visits == 0:
            return 0.0

        # 计算各时间段的访问比例
        morning_ratio = time_patterns['morning_visits'] / total_visits
        afternoon_ratio = time_patterns['afternoon_visits'] / total_visits
        weekday_ratio = time_patterns['weekday_visits'] / total_visits
        weekend_ratio = time_patterns['weekend_visits'] / total_visits

        # 工作特征权重计算
        # 1. 工作日访问比例（权重40%）- 工作主要在工作日
        weekday_score = weekday_ratio

        # 2. 白天工作时间访问比例（权重35%）- 上午+下午
        work_hours_score = morning_ratio + afternoon_ratio

        # 3. 停留时长评分（权重15%）- 工作日停留4-10小时较合理
        if 3 <= avg_stay_hours <= 12:
            stay_score = min(avg_stay_hours / 8.0, 1.0)
        else:
            stay_score = 0.3  # 不在合理范围内，给较低分数

        # 4. 周末访问惩罚（权重10%）- 周末很少去工作地点
        weekend_penalty = 1.0 - weekend_ratio

        # 综合评分
        work_score = (weekday_score * 0.4 +
                     work_hours_score * 0.35 +
                     stay_score * 0.15 +
                     weekend_penalty * 0.1)

        # 访问频率要求：工作地点应该有一定的访问频率
        if total_visits < 10:
            work_score *= 0.5  # 访问次数太少，降低得分

        return min(max(work_score, 0.0), 1.0)

    def generate_report(self, key_locations: Dict[str, Any], cluster_analysis: List[Dict]) -> Dict[str, Any]:
        """生成分析报告"""
        report = {
            'summary': {
                'total_records': len(self.data),
                'date_range': {
                    'start': str(self.data['timestamp'].min()),
                    'end': str(self.data['timestamp'].max())
                },
                'total_clusters': len([c for c in cluster_analysis if c['cluster_id'] != -1])
            },
            'key_locations': {},
            'all_clusters': [
                {
                    **cluster,
                    'first_visit': str(cluster.get('first_visit', '')),
                    'last_visit': str(cluster.get('last_visit', ''))
                }
                for cluster in cluster_analysis
            ]
        }

        # 家庭住址
        if key_locations['home']:
            home = key_locations['home']
            report['key_locations']['home'] = {
                'coordinates': home['coordinates'],
                'total_visits': home['total_visits'],
                'avg_stay_hours': round(home['avg_stay_hours'], 2),
                'visit_frequency_per_day': round(home['visit_frequency_per_day'], 2),
                'confidence_score': round(home.get('home_score', 0), 2)
            }

        # 工作单位
        if key_locations['work']:
            work = key_locations['work']
            report['key_locations']['work'] = {
                'coordinates': work['coordinates'],
                'total_visits': work['total_visits'],
                'avg_stay_hours': round(work['avg_stay_hours'], 2),
                'visit_frequency_per_day': round(work['visit_frequency_per_day'], 2),
                'confidence_score': round(work.get('work_score', 0), 2)
            }

        # 其他重要地点
        report['key_locations']['other_important'] = []
        for location in key_locations['other_important'][:self.config.MAX_OTHER_LOCATIONS]:
            report['key_locations']['other_important'].append({
                'coordinates': location['coordinates'],
                'total_visits': location['total_visits'],
                'avg_stay_hours': round(location['avg_stay_hours'], 2),
                'visit_frequency_per_day': round(location.get('visit_frequency_per_day', 0), 2),
                'confidence_score': round(location.get('confidence_score', 0), 2)
            })

        return report

    def save_report(self, report, filename='location_analysis_report.json'):
        """保存分析报告"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"分析报告已保存到: {filename}")

    def visualize_clusters(self, save_path: str = 'location_analysis_visualization.png') -> bool:
        """可视化聚类结果

        Args:
            save_path: 保存路径

        Returns:
            bool: 是否成功生成可视化
        """
        if self.data is None or self.cluster_centers is None:
            self.logger.error("请先加载数据并进行聚类分析")
            return False

        try:
            self.logger.info("开始生成可视化图表")

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建子图
            _, axes = plt.subplots(2, 2, figsize=self.config.FIGURE_SIZE)

            # 1. 地理位置分布图
            self._plot_location_distribution(axes[0, 0])

            # 2. 时间分布图
            self._plot_time_distribution(axes[0, 1])

            # 3. 工作日vs周末分布
            self._plot_weekday_distribution(axes[1, 0])

            # 4. 聚类统计图
            self._plot_cluster_statistics(axes[1, 1])

            plt.tight_layout()
            plt.savefig(save_path, dpi=self.config.DPI, bbox_inches='tight')
            self.logger.info(f"可视化图表已保存到: {save_path}")

            # 显示图表（如果在交互环境中）
            try:
                plt.show()
            except:
                pass  # 非交互环境中忽略显示错误

            return True

        except Exception as e:
            self.logger.error(f"生成可视化图表失败: {e}")
            return False

    def _plot_location_distribution(self, ax) -> None:
        """绘制地理位置分布图"""
        # 绘制所有点
        scatter = ax.scatter(self.data['longitude'], self.data['latitude'],
                           c=self.data['cluster'], cmap='tab10', alpha=0.6, s=15)

        # 标记聚类中心
        valid_centers = [c for c in self.cluster_centers if c['cluster_id'] != -1]
        for center in valid_centers:
            ax.scatter(center['longitude'], center['latitude'],
                      c='red', s=200, marker='*', linewidths=2,
                      edgecolors='black', label='聚类中心' if center == valid_centers[0] else "")

        ax.set_xlabel('经度')
        ax.set_ylabel('纬度')
        ax.set_title('位置聚类分布图')
        ax.grid(True, alpha=0.3)
        if valid_centers:
            ax.legend()

        # 添加颜色条
        plt.colorbar(scatter, ax=ax, label='聚类ID')

    def _plot_time_distribution(self, ax) -> None:
        """绘制时间分布图"""
        hours = self.data['hour']
        ax.hist(hours, bins=24, edgecolor='black', alpha=0.7, color='skyblue')
        ax.set_xlabel('小时')
        ax.set_ylabel('记录数量')
        ax.set_title('24小时活动分布')
        ax.set_xticks(range(0, 24, 2))
        ax.grid(True, alpha=0.3)

    def _plot_weekday_distribution(self, ax) -> None:
        """绘制工作日分布图"""
        weekday_counts = self.data['day_of_week'].value_counts().sort_index()
        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

        bars = ax.bar(weekday_names, weekday_counts.values,
                     color=['lightcoral' if i >= 5 else 'lightblue' for i in range(7)],
                     edgecolor='black')
        ax.set_xlabel('星期')
        ax.set_ylabel('记录数量')
        ax.set_title('一周活动分布')
        ax.tick_params(axis='x', rotation=45)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, count in zip(bars, weekday_counts.values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + count*0.01,
                   str(count), ha='center', va='bottom')

    def _plot_cluster_statistics(self, ax) -> None:
        """绘制聚类统计图"""
        valid_centers = [c for c in self.cluster_centers if c['cluster_id'] != -1]

        if not valid_centers:
            ax.text(0.5, 0.5, '没有有效聚类', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('聚类统计')
            return

        cluster_ids = [f"聚类{c['cluster_id']}" for c in valid_centers]
        cluster_sizes = [c['count'] for c in valid_centers]

        bars = ax.bar(cluster_ids, cluster_sizes, color='lightgreen', edgecolor='black')
        ax.set_xlabel('聚类')
        ax.set_ylabel('记录数量')
        ax.set_title('各聚类点记录数量')
        ax.tick_params(axis='x', rotation=45)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, size in zip(bars, cluster_sizes):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + size*0.01,
                   str(size), ha='center', va='bottom')

    def analyze_complete(self, file_path: str, output_dir: str = './results') -> Dict[str, Any]:
        """
        完整的分析流程，包含地理位置反查

        Args:
            file_path: 数据文件路径
            output_dir: 输出目录

        Returns:
            Dict[str, Any]: 完整的分析结果
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        try:
            # 1. 加载数据
            self.logger.info("开始完整分析流程")
            if not self.load_data(file_path):
                raise ValueError("数据加载失败")

            # 2. 聚类分析
            cluster_centers = self.cluster_locations()
            if cluster_centers is None:
                raise ValueError("聚类分析失败")

            # 3. 模式分析
            cluster_analysis = self.analyze_cluster_patterns()
            if cluster_analysis is None:
                raise ValueError("模式分析失败")

            # 4. 增强聚类分析（添加地理位置信息）
            enhanced_cluster_analysis = self.enhance_cluster_analysis(cluster_analysis)

            # 5. 关键地点识别
            key_locations = self.identify_key_locations(enhanced_cluster_analysis)

            # 6. 生成增强版报告
            enhanced_report = self.generate_enhanced_report(key_locations, enhanced_cluster_analysis)

            # 7. 保存结果
            report_path = output_path / 'enhanced_location_analysis_report.json'
            self.save_report(enhanced_report, str(report_path))

            # 8. 生成可视化
            viz_path = output_path / 'enhanced_location_analysis_visualization.png'
            self.visualize_clusters(str(viz_path))

            # 9. 打印增强版摘要
            self.print_enhanced_summary(key_locations, enhanced_cluster_analysis)

            self.logger.info("完整分析流程执行成功")

            return {
                'success': True,
                'report': enhanced_report,
                'key_locations': key_locations,
                'enhanced_cluster_analysis': enhanced_cluster_analysis,
                'output_files': {
                    'report': str(report_path),
                    'visualization': str(viz_path)
                }
            }

        except Exception as e:
            self.logger.error(f"分析流程失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def analyze_simple(self, file_path: str) -> Dict[str, Any]:
        """
        简化的分析流程，返回关键结果

        Args:
            file_path: 数据文件路径

        Returns:
            Dict[str, Any]: 分析结果摘要
        """
        try:
            # 执行完整分析
            result = self.analyze_complete(file_path)

            if not result['success']:
                return result

            # 提取关键信息
            key_locations = result['key_locations']

            summary = {
                'success': True,
                'home': None,
                'work': None,
                'other_important': []
            }

            # 提取家庭住址信息
            if key_locations['home']:
                home = key_locations['home']
                location_info = home.get('location_info', {})
                summary['home'] = {
                    'coordinates': home['coordinates'],
                    'address': location_info.get('summary_address', '未知地址'),
                    'full_address': location_info.get('formatted_address', ''),
                    'visits': home['total_visits'],
                    'confidence': home.get('home_score', 0)
                }

            # 提取工作地点信息
            if key_locations['work']:
                work = key_locations['work']
                location_info = work.get('location_info', {})
                summary['work'] = {
                    'coordinates': work['coordinates'],
                    'address': location_info.get('summary_address', '未知地址'),
                    'full_address': location_info.get('formatted_address', ''),
                    'visits': work['total_visits'],
                    'confidence': work.get('work_score', 0)
                }

            # 提取其他重要地点
            for location in key_locations['other_important']:
                location_info = location.get('location_info', {})
                summary['other_important'].append({
                    'coordinates': location['coordinates'],
                    'address': location_info.get('summary_address', '未知地址'),
                    'full_address': location_info.get('formatted_address', ''),
                    'visits': location['total_visits']
                })

            return summary

        except Exception as e:
            self.logger.error(f"简化分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


def create_sample_data(filename: str = 'sample_location_data.csv',
                      n_days: int = 30,
                      base_lat: float = 39.9042,
                      base_lon: float = 116.4074) -> str:
    """创建更真实的示例数据用于测试

    Args:
        filename: 输出文件名
        n_days: 生成数据的天数
        base_lat: 基准纬度
        base_lon: 基准经度

    Returns:
        str: 生成的文件路径
    """
    np.random.seed(42)

    print(f"正在生成 {n_days} 天的示例位置数据...")

    all_data = []

    # 定义地点（减小标准差以产生更密集的聚类）
    locations = {
        'home': {'lat': base_lat, 'lon': base_lon, 'std': 0.0003},
        'work': {'lat': base_lat + 0.01, 'lon': base_lon - 0.008, 'std': 0.0002},
        'gym': {'lat': base_lat - 0.005, 'lon': base_lon + 0.006, 'std': 0.0002},
        'shopping': {'lat': base_lat + 0.008, 'lon': base_lon + 0.012, 'std': 0.0003},
        'restaurant': {'lat': base_lat - 0.003, 'lon': base_lon - 0.004, 'std': 0.0002}
    }

    start_date = datetime.now() - timedelta(days=n_days)

    for day in range(n_days):
        current_date = start_date + timedelta(days=day)
        is_weekend = current_date.weekday() >= 5

        # 生成一天的轨迹
        day_data = _generate_day_trajectory(current_date, locations, is_weekend)
        all_data.extend(day_data)

    # 创建DataFrame
    df = pd.DataFrame(all_data)
    df = df.sort_values('timestamp').reset_index(drop=True)

    # 添加一些噪声数据（随机位置）
    noise_count = len(df) // 20  # 5%的噪声
    noise_data = []
    for _ in range(noise_count):
        noise_time = start_date + timedelta(
            days=np.random.randint(0, n_days),
            hours=np.random.randint(0, 24),
            minutes=np.random.randint(0, 60)
        )
        noise_data.append({
            'timestamp': noise_time,
            'latitude': base_lat + np.random.normal(0, 0.02),
            'longitude': base_lon + np.random.normal(0, 0.02)
        })

    # 合并噪声数据
    noise_df = pd.DataFrame(noise_data)
    df = pd.concat([df, noise_df], ignore_index=True)
    df = df.sort_values('timestamp').reset_index(drop=True)

    # 保存数据
    df.to_csv(filename, index=False)
    print(f"✅ 示例数据已保存到: {filename}")
    print(f"📊 生成了 {len(df)} 条位置记录")
    print(f"📅 时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")

    return filename

def _generate_day_trajectory(date: datetime, locations: Dict, is_weekend: bool) -> List[Dict]:
    """生成一天的轨迹数据"""
    day_data = []

    if is_weekend:
        # 周末模式：更多在家时间，偶尔外出
        # 早上在家
        day_data.extend(_generate_location_visits(
            date, locations['home'],
            start_hour=7, end_hour=10, frequency=0.8
        ))

        # 可能去健身房
        if np.random.random() < 0.4:
            day_data.extend(_generate_location_visits(
                date, locations['gym'],
                start_hour=10, end_hour=12, frequency=1.0
            ))

        # 可能去购物
        if np.random.random() < 0.6:
            day_data.extend(_generate_location_visits(
                date, locations['shopping'],
                start_hour=14, end_hour=17, frequency=0.8
            ))

        # 晚上在家
        day_data.extend(_generate_location_visits(
            date, locations['home'],
            start_hour=18, end_hour=23, frequency=0.9
        ))

    else:
        # 工作日模式
        # 早上在家
        day_data.extend(_generate_location_visits(
            date, locations['home'],
            start_hour=6, end_hour=8, frequency=0.8
        ))

        # 工作时间
        day_data.extend(_generate_location_visits(
            date, locations['work'],
            start_hour=9, end_hour=18, frequency=1.0
        ))

        # 可能去餐厅
        if np.random.random() < 0.3:
            day_data.extend(_generate_location_visits(
                date, locations['restaurant'],
                start_hour=12, end_hour=13, frequency=1.0
            ))

        # 晚上回家
        day_data.extend(_generate_location_visits(
            date, locations['home'],
            start_hour=19, end_hour=23, frequency=0.9
        ))

    return day_data

def _generate_location_visits(date: datetime, location: Dict,
                            start_hour: int, end_hour: int, frequency: float) -> List[Dict]:
    """生成特定时间段在特定地点的访问记录"""
    visits = []
    current_hour = start_hour

    while current_hour < end_hour:
        if np.random.random() < frequency:
            # 生成该小时内的随机时间点
            minutes = np.random.randint(0, 60)
            visit_time = date.replace(hour=current_hour, minute=minutes, second=0, microsecond=0)

            # 添加位置噪声
            lat = location['lat'] + np.random.normal(0, location['std'])
            lon = location['lon'] + np.random.normal(0, location['std'])

            visits.append({
                'timestamp': visit_time,
                'latitude': lat,
                'longitude': lon
            })

        current_hour += 1

    return visits


class EnhancedLocationAnalyzer(LocationAnalyzer):
    """增强版位置分析器，包含所有增强功能"""

    def enhance_cluster_analysis(self, cluster_analysis: List[Dict]) -> List[Dict]:
        """
        增强聚类分析结果，添加地理位置信息

        Args:
            cluster_analysis: 原始聚类分析结果

        Returns:
            List[Dict]: 增强后的聚类分析结果
        """
        if not cluster_analysis:
            return cluster_analysis

        self.logger.info("开始为聚类结果添加地理位置信息")

        enhanced_analysis = []

        for cluster in cluster_analysis:
            if cluster['cluster_id'] == -1:  # 跳过噪声点
                enhanced_analysis.append(cluster)
                continue

            lat, lon = cluster['coordinates']
            self.logger.info(f"查询聚类 {cluster['cluster_id']} 的地理位置: ({lat:.4f}, {lon:.4f})")

            # 获取地理位置信息
            location_info = self.geocoding_service.reverse_geocode(lat, lon)

            # 增强聚类信息
            enhanced_cluster = cluster.copy()

            if location_info:
                enhanced_cluster['location_info'] = {
                    'formatted_address': location_info.formatted_address,
                    'summary_address': self.geocoding_service.get_location_summary(location_info),
                    'country': location_info.country,
                    'province': location_info.administrative_area_level_1,
                    'city': location_info.administrative_area_level_2,
                    'district': location_info.administrative_area_level_3,
                    'locality': location_info.locality,
                    'sublocality': location_info.sublocality,
                    'route': location_info.route,
                    'street_number': location_info.street_number,
                    'postal_code': location_info.postal_code,
                    'place_types': location_info.place_types
                }

                self.logger.info(f"✅ 聚类 {cluster['cluster_id']}: {location_info.formatted_address}")
            else:
                enhanced_cluster['location_info'] = {
                    'formatted_address': f"未知位置 ({lat:.4f}, {lon:.4f})",
                    'summary_address': f"坐标: ({lat:.4f}, {lon:.4f})",
                    'error': '地理位置查询失败'
                }

                self.logger.warning(f"❌ 聚类 {cluster['cluster_id']}: 地理位置查询失败")

            enhanced_analysis.append(enhanced_cluster)

        # 保存地理位置缓存
        self.geocoding_service._save_cache()

        self.logger.info(f"完成 {len(enhanced_analysis)} 个聚类的地理位置增强")
        return enhanced_analysis
    def geng_le(self):
        print()
    def generate_enhanced_report(self, key_locations: Dict[str, Any],
                               enhanced_cluster_analysis: List[Dict]) -> Dict[str, Any]:
        """
        生成增强版分析报告，包含地理位置信息

        Args:
            key_locations: 关键地点识别结果
            enhanced_cluster_analysis: 增强后的聚类分析结果

        Returns:
            Dict[str, Any]: 增强版分析报告
        """
        # 生成基础报告
        report = self.generate_report(key_locations, enhanced_cluster_analysis)

        # 增强关键地点信息
        for location_type in ['home', 'work']:
            if key_locations.get(location_type):
                location = key_locations[location_type]

                # 查找对应的增强聚类信息
                cluster_id = location['cluster_id']
                enhanced_cluster = next(
                    (c for c in enhanced_cluster_analysis if c['cluster_id'] == cluster_id),
                    None
                )

                if enhanced_cluster and 'location_info' in enhanced_cluster:
                    location_info = enhanced_cluster['location_info']
                    report['key_locations'][location_type]['location_info'] = location_info

                    # 更新地址显示
                    if 'summary_address' in location_info:
                        report['key_locations'][location_type]['address'] = location_info['summary_address']

        # 增强其他重要地点信息
        enhanced_other_locations = []
        for location in key_locations.get('other_important', []):
            cluster_id = location['cluster_id']
            enhanced_cluster = next(
                (c for c in enhanced_cluster_analysis if c['cluster_id'] == cluster_id),
                None
            )

            enhanced_location = {
                'coordinates': location['coordinates'],
                'total_visits': location['total_visits'],
                'avg_stay_hours': round(location['avg_stay_hours'], 2),
                'visit_frequency_per_day': round(location.get('visit_frequency_per_day', 0), 2),
                'confidence_score': round(location.get('confidence_score', 0), 2)
            }

            if enhanced_cluster and 'location_info' in enhanced_cluster:
                location_info = enhanced_cluster['location_info']
                enhanced_location['location_info'] = location_info
                if 'summary_address' in location_info:
                    enhanced_location['address'] = location_info['summary_address']

            enhanced_other_locations.append(enhanced_location)

        report['key_locations']['other_important'] = enhanced_other_locations

        # 添加地理位置服务统计
        geocoding_stats = self.geocoding_service.get_cache_stats()
        report['geocoding_stats'] = geocoding_stats

        return report

    def print_enhanced_summary(self, key_locations: Dict[str, Any],
                             enhanced_cluster_analysis: List[Dict]) -> None:
        """打印增强版分析结果摘要"""
        print("\n" + "="*60)
        print("🌍 增强版地理位置分析结果摘要")
        print("="*60)

        # 家庭住址
        if key_locations['home']:
            home = key_locations['home']
            cluster_id = home['cluster_id']
            enhanced_cluster = next(
                (c for c in enhanced_cluster_analysis if c['cluster_id'] == cluster_id),
                None
            )

            print(f"🏠 家庭住址:")
            print(f"   坐标: {home['coordinates']}")
            print(f"   访问次数: {home['total_visits']}, 置信度: {home.get('home_score', 0):.2f}")

            if enhanced_cluster and 'location_info' in enhanced_cluster:
                location_info = enhanced_cluster['location_info']
                if 'summary_address' in location_info:
                    print(f"   地址: {location_info['summary_address']}")
                if 'formatted_address' in location_info:
                    print(f"   详细地址: {location_info['formatted_address']}")
        else:
            print("🏠 家庭住址: 未识别")

        # 工作地点
        if key_locations['work']:
            work = key_locations['work']
            cluster_id = work['cluster_id']
            enhanced_cluster = next(
                (c for c in enhanced_cluster_analysis if c['cluster_id'] == cluster_id),
                None
            )

            print(f"🏢 工作地点:")
            print(f"   坐标: {work['coordinates']}")
            print(f"   访问次数: {work['total_visits']}, 置信度: {work.get('work_score', 0):.2f}")

            if enhanced_cluster and 'location_info' in enhanced_cluster:
                location_info = enhanced_cluster['location_info']
                if 'summary_address' in location_info:
                    print(f"   地址: {location_info['summary_address']}")
                if 'formatted_address' in location_info:
                    print(f"   详细地址: {location_info['formatted_address']}")
        else:
            print("🏢 工作地点: 未识别")

        # 其他重要地点
        other_count = len(key_locations['other_important'])
        print(f"📌 其他重要地点: {other_count}个")

        for i, location in enumerate(key_locations['other_important'][:3], 1):
            cluster_id = location['cluster_id']
            enhanced_cluster = next(
                (c for c in enhanced_cluster_analysis if c['cluster_id'] == cluster_id),
                None
            )

            print(f"   地点{i}: {location['coordinates']} (访问{location['total_visits']}次)")

            if enhanced_cluster and 'location_info' in enhanced_cluster:
                location_info = enhanced_cluster['location_info']
                if 'summary_address' in location_info:
                    print(f"          地址: {location_info['summary_address']}")

        if other_count > 3:
            print(f"   ... 还有{other_count - 3}个地点")

        # 地理位置服务统计
        stats = self.geocoding_service.get_cache_stats()
        print(f"\n📊 地理位置查询统计:")
        print(f"   缓存记录: {stats['cache_size']} 条")
        print(f"   本次请求: {stats['request_count']} 次")


def print_analysis_summary(key_locations: Dict[str, Any]) -> None:
    """打印分析结果摘要"""
    print("\n" + "="*50)
    print("📍 地理位置分析结果摘要")
    print("="*50)

    if key_locations['home']:
        home = key_locations['home']
        print(f"🏠 家庭住址: {home['coordinates']}")
        print(f"   访问次数: {home['total_visits']}, 置信度: {home.get('home_score', 0):.2f}")
    else:
        print("🏠 家庭住址: 未识别")

    if key_locations['work']:
        work = key_locations['work']
        print(f"🏢 工作地点: {work['coordinates']}")
        print(f"   访问次数: {work['total_visits']}, 置信度: {work.get('work_score', 0):.2f}")
    else:
        print("🏢 工作地点: 未识别")

    other_count = len(key_locations['other_important'])
    print(f"📌 其他重要地点: {other_count}个")
    for i, loc in enumerate(key_locations['other_important'][:3], 1):
        print(f"   地点{i}: {loc['coordinates']} (访问{loc['total_visits']}次)")

    if other_count > 3:
        print(f"   ... 还有{other_count - 3}个地点")


def main():
    """演示用法"""
    print("🌍 增强版地理位置分析工具演示")
    print("=" * 50)

    # 创建示例数据
    sample_file = create_sample_data(n_days=15)

    # 创建分析器（使用测试API密钥）
    analyzer = LocationAnalyzer(
        min_cluster_size=3,  # HDBSCAN最小聚类大小
        cluster_selection_epsilon=500,  # 500米聚类选择阈值
        google_api_key="YOUR_GOOGLE_MAPS_API_KEY"  # 请替换为真实API密钥
    )

    print("\n📊 执行简化分析...")
    # 执行简化分析
    result = analyzer.analyze_simple(sample_file)

    if result['success']:
        print("✅ 分析成功！")

        if result['home']:
            print(f"🏠 家庭住址: {result['home']['address']}")
            print(f"   访问次数: {result['home']['visits']}")

        if result['work']:
            print(f"🏢 工作地点: {result['work']['address']}")
            print(f"   访问次数: {result['work']['visits']}")

        print(f"📌 其他重要地点: {len(result['other_important'])}个")

    else:
        print(f"❌ 分析失败: {result['error']}")

    print("\n💡 使用提示:")
    print("1. 设置真实的Google Maps API密钥以获取地理位置信息")
    print("2. 使用 analyzer.analyze_complete() 获取完整分析结果")
    print("3. 使用 analyzer.analyze_simple() 获取简化结果")


# if __name__ == "__main__":
#     main()
