#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenStreetMap 地理位置反查服务模块
使用 OpenStreetMap Nominatim API 通过经纬度获取具体的地理位置信息
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import pickle
from .location_info import LocationInfo


class OpenStreetGeocodingService:
    """OpenStreetMap 地理位置反查服务类"""
    
    def __init__(self, cache_file: str = "geocoding_osm_cache.pkl",
                 language: str = "zh"):
        """
        初始化地理位置反查服务
        
        Args:
            cache_file: 缓存文件路径
            language: 返回结果的语言
        """
        self.base_url = "https://nominatim.openstreetmap.org/reverse"
        self.cache_file = Path(cache_file)
        self.language = language
        self.request_count = 0
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
        # 加载缓存
        self.cache = self._load_cache()
    
    def _load_cache(self) -> Dict:
        """加载缓存数据"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            self.logger.warning(f"加载缓存失败: {e}")
        return {}
    
    def _save_cache(self):
        """保存缓存数据"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def _get_cache_key(self, latitude: float, longitude: float, precision: int = 4) -> str:
        """
        生成缓存键，使用指定精度避免重复查询相近位置
        """
        lat = round(latitude, precision)
        lng = round(longitude, precision)
        return f"{lat:.{precision}f},{lng:.{precision}f}"
    
    def _rate_limit(self):
        """API请求频率限制"""
        time.sleep(1)  # Nominatim要求每秒不超过1个请求
    
    def reverse_geocode(self, latitude: float, longitude: float, 
                       use_cache: bool = True) -> Optional[LocationInfo]:
        """
        反向地理编码：通过经纬度获取地理位置信息
        
        Args:
            latitude: 纬度
            longitude: 经度
            use_cache: 是否使用缓存
            
        Returns:
            LocationInfo: 地理位置信息，失败时返回None
        """
        cache_key = self._get_cache_key(latitude, longitude)
        
        # 检查缓存
        if use_cache and cache_key in self.cache:
            self.logger.debug(f"从缓存中获取位置信息: {cache_key}")
            return self.cache[cache_key]
        
        # 准备请求参数
        params = {
            'lat': latitude,
            'lon': longitude,
            'format': 'json',
            'addressdetails': 1,
            'accept-language': self.language,
            'zoom': 18
        }
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        try:
            # 限制请求频率
            self._rate_limit()
            self.request_count += 1
            
            # 发送请求
            response = requests.get(
                self.base_url, 
                params=params, 
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            print("请求返回值:",data)
            if 'error' in data:
                self.logger.error(f"API返回错误: {data.get('error', 'Unknown error')}")
                return None
            
            # 解析响应
            location_info = self._parse_response(data, latitude, longitude)
            
            # 保存到缓存
            if location_info and use_cache:
                self.cache[cache_key] = location_info
                self._save_cache()
                
            return location_info
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {e}")
            return None
    
    def _parse_response(self, result: Dict[str, Any], 
                       latitude: float, longitude: float) -> Optional[LocationInfo]:
        """
        解析OpenStreetMap API响应
        
        Args:
            result: API返回的JSON数据
            latitude: 纬度
            longitude: 经度
            
        Returns:
            LocationInfo: 解析后的位置信息
        """
        try:
            address = result.get('address', {})
            
            # 构建位置信息
            location_info = LocationInfo(
                latitude=latitude,
                longitude=longitude,
                formatted_address=result.get('display_name', ''),
                country=address.get('country', ''),
                administrative_area_level_1=address.get('state', ''),
                administrative_area_level_2=address.get('city', address.get('town', '')),
                administrative_area_level_3=address.get('suburb', ''),
                locality=address.get('city', address.get('town', '')),
                village=address.get('village', ''),
                sublocality=address.get('suburb', ''),
                neighborhood=address.get('neighborhood', ''),
                route=address.get('road', ''),
                street_number=address.get('house_number', ''),
                amenity=address.get('amenity', ''),
                postal_code=address.get('postcode', '')
            )
            print("location_info:",location_info)
            return location_info
            
        except Exception as e:
            self.logger.error(f"解析响应失败: {e}")
            return None
    
    def get_location_summary(self, location_info: LocationInfo) -> str:
        """
        获取位置信息摘要
        
        Args:
            location_info: 位置信息对象
            
        Returns:
            str: 格式化后的位置摘要，从大到小显示行政区划
        """
        parts = []
        print("location_info:",location_info)
        # 从大到小的行政区划
        if location_info.country:
            if location_info.country == "臺灣":
                parts.append("中国台湾省")
            else:
                parts.append(location_info.country)
            
            print(location_info.country)
        if location_info.administrative_area_level_1:
            parts.append(location_info.administrative_area_level_1)
            print(location_info.administrative_area_level_1)
        if location_info.administrative_area_level_2:
            parts.append(location_info.administrative_area_level_2)
            print(location_info.administrative_area_level_2)
        if location_info.administrative_area_level_3:
            parts.append(location_info.administrative_area_level_3) 
        if location_info.village:
            parts.append(location_info.village)
            print(location_info.village)
        if location_info.sublocality:
            parts.append(location_info.sublocality)
            print(location_info.sublocality)
        if location_info.neighborhood:
            parts.append(location_info.neighborhood)
            print(location_info.neighborhood)
        if location_info.route:
            parts.append(location_info.route)
            print(location_info.route)
        if location_info.street_number:
            parts.append(location_info.street_number+"号")
            print(location_info.street_number)
        if location_info.amenity:
            parts.append(location_info.amenity) 
            print(location_info.amenity)
        
        # 去重并保持顺序
        seen = set()
        unique_parts = []
        for part in parts:
            if part not in seen:
                seen.add(part)
                unique_parts.append(part)
        
        
        return "".join(unique_parts)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict: 包含缓存统计信息的字典
        """
        return {
            'cache_size': len(self.cache),
            'cache_file': str(self.cache_file),
            'cache_file_exists': self.cache_file.exists(),
            'request_count': self.request_count
        }

def main():
    """示例用法"""
    print("🌍 OpenStreetMap 地理位置反查服务示例")
    print("=" * 50 + "\n")
    
    # 创建服务实例
    service = OpenStreetGeocodingService()
    
    # 测试坐标列表
    test_coordinates = [
        (39.9042, 116.4074),  # 北京
        (25.036703511618263, 121.51757310542556),  # 台北
        (31.2304, 121.4737),  # 上海
    ]
    
    # 单个查询示例
    lat, lng = test_coordinates[1]
    print(f"📍 查询坐标: ({lat}, {lng})")
    
    location_info = service.reverse_geocode(lat, lng)
    
    if location_info:
        print(f"完整地址: {location_info.formatted_address}")
        from opencc import OpenCC
        cc = OpenCC('t2s')  # 繁体转简体（t: Traditional, s: Simplified）
        zydz = cc.convert(service.get_location_summary(location_info))
        print("zydz:",zydz)
        print(f"摘要地址: {zydz}")
        print(f"国家: {location_info.country}")
        print(f"省/直辖市: {location_info.administrative_area_level_1}")
        print(f"市: {location_info.administrative_area_level_2}")
        print(f"区: {location_info.administrative_area_level_3}")
    else:
        print("❌ 获取位置信息失败")
    
    # 显示缓存统计
    stats = service.get_cache_stats()
    print(f"\n📊 缓存统计: {json.dumps(stats, ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    main()
