#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理位置反查服务模块
使用Google Maps API通过经纬度获取具体的地理位置信息
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import pickle
from location_info import LocationInfo


class GeocodingService:
    """地理位置反查服务类"""
    
    def __init__(self, api_key: str = "AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI", 
                 cache_file: str = "geocoding_cache.pkl",
                 language: str = "zh-CN"):
        """
        初始化地理位置反查服务
        
        Args:
            api_key: Google Maps API密钥
            cache_file: 缓存文件路径
            language: 返回结果的语言
        """
        self.api_key = api_key
        self.base_url = "https://maps.googleapis.com/maps/api/geocode/json"
        self.cache_file = Path(cache_file)
        self.language = language
        self.cache = self._load_cache()
        self.request_count = 0
        self.max_requests_per_second = 10  # API限制
        
        # 设置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def _load_cache(self) -> Dict[str, LocationInfo]:
        """加载缓存数据"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info(f"已加载 {len(cache)} 条缓存记录")
                return cache
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
        return {}
    
    def _save_cache(self) -> None:
        """保存缓存数据"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
            self.logger.info(f"已保存 {len(self.cache)} 条缓存记录")
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def _get_cache_key(self, latitude: float, longitude: float, precision: int = 4) -> str:
        """生成缓存键，使用指定精度避免重复查询相近位置"""
        lat_rounded = round(latitude, precision)
        lon_rounded = round(longitude, precision)
        return f"{lat_rounded},{lon_rounded}"
    
    def _rate_limit(self) -> None:
        """API请求频率限制"""
        if self.request_count >= self.max_requests_per_second:
            time.sleep(1)
            self.request_count = 0
    
    def reverse_geocode(self, latitude: float, longitude: float, 
                       use_cache: bool = True) -> Optional[LocationInfo]:
        """
        反向地理编码：通过经纬度获取地理位置信息
        
        Args:
            latitude: 纬度
            longitude: 经度
            use_cache: 是否使用缓存
            
        Returns:
            LocationInfo: 地理位置信息，失败时返回None
        """
        # 检查缓存
        cache_key = self._get_cache_key(latitude, longitude)
        if use_cache and cache_key in self.cache:
            self.logger.debug(f"从缓存获取位置信息: {cache_key}")
            return self.cache[cache_key]
 
        # 频率限制
        self._rate_limit()
        
        # 构建请求参数
        params = {
            'latlng': f"{latitude},{longitude}",
            'key': self.api_key,
            'language': self.language,
            'result_type': 'street_address|route|neighborhood|locality|administrative_area_level_3|administrative_area_level_2|administrative_area_level_1|country'
        }
        
        try:
            self.logger.debug(f"请求地理位置信息: ({latitude}, {longitude})")
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            self.request_count += 1
            data = response.json()
            print(data)
            if data['status'] == 'OK' and data['results']:
                location_info = self._parse_response(data['results'][0], latitude, longitude)
                
                # 保存到缓存
                if use_cache:
                    self.cache[cache_key] = location_info
                    
                self.logger.info(f"成功获取位置信息: {location_info.formatted_address}")
                return location_info
            else:
                self.logger.warning(f"API返回错误: {data.get('status', 'UNKNOWN')}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"反向地理编码失败: {e}")
            return None
    
    def _parse_response(self, result: Dict[str, Any], latitude: float, longitude: float) -> LocationInfo:
        """解析Google Maps API响应"""
        location_info = LocationInfo(
            latitude=latitude,
            longitude=longitude,
            formatted_address=result.get('formatted_address', ''),
            place_types=result.get('types', [])
        )
        
        # 解析地址组件
        for component in result.get('address_components', []):
            types = component.get('types', [])
            long_name = component.get('long_name', '')
            
            if 'country' in types:
                location_info.country = long_name
            elif 'administrative_area_level_1' in types:
                location_info.administrative_area_level_1 = long_name
            elif 'administrative_area_level_2' in types:
                location_info.administrative_area_level_2 = long_name
            elif 'administrative_area_level_3' in types:
                location_info.administrative_area_level_3 = long_name
            elif 'locality' in types:
                location_info.locality = long_name
            elif 'sublocality' in types or 'sublocality_level_1' in types:
                location_info.sublocality = long_name
            elif 'route' in types:
                location_info.route = long_name
            elif 'street_number' in types:
                location_info.street_number = long_name
            elif 'postal_code' in types:
                location_info.postal_code = long_name
        
        return location_info
    
    def batch_reverse_geocode(self, coordinates: List[Tuple[float, float]], 
                             delay: float = 0.1) -> List[Optional[LocationInfo]]:
        """
        批量反向地理编码
        
        Args:
            coordinates: 坐标列表 [(lat, lon), ...]
            delay: 请求间隔时间（秒）
            
        Returns:
            List[Optional[LocationInfo]]: 地理位置信息列表
        """
        results = []
        total = len(coordinates)
        
        self.logger.info(f"开始批量地理位置查询，共 {total} 个坐标")
        
        for i, (lat, lon) in enumerate(coordinates, 1):
            self.logger.info(f"处理进度: {i}/{total} ({i/total*100:.1f}%)")
            
            location_info = self.reverse_geocode(lat, lon)
            results.append(location_info)
            
            # 请求间隔
            if delay > 0 and i < total:
                time.sleep(delay)
        
        # 保存缓存
        self._save_cache()
        
        self.logger.info(f"批量查询完成，成功 {sum(1 for r in results if r is not None)} 个")
        return results
    
    def get_location_summary(self, location_info: LocationInfo) -> str:
        """获取位置信息摘要"""
        if not location_info:
            return "未知位置"
        
        # 构建层次化地址
        parts = []
        
        if location_info.street_number and location_info.route:
            parts.append(f"{location_info.route}{location_info.street_number}")
        elif location_info.route:
            parts.append(location_info.route)
        
        if location_info.sublocality:
            parts.append(location_info.sublocality)
        
        if location_info.administrative_area_level_3:
            parts.append(location_info.administrative_area_level_3)
        
        if location_info.administrative_area_level_2:
            parts.append(location_info.administrative_area_level_2)
        
        if location_info.administrative_area_level_1:
            parts.append(location_info.administrative_area_level_1)
        
        return ", ".join(parts) if parts else location_info.formatted_address
    
    def export_cache_to_json(self, output_file: str = "geocoding_cache.json") -> None:
        """导出缓存到JSON文件"""
        try:
            cache_data = {}
            for key, location_info in self.cache.items():
                cache_data[key] = {
                    'latitude': location_info.latitude,
                    'longitude': location_info.longitude,
                    'formatted_address': location_info.formatted_address,
                    'country': location_info.country,
                    'administrative_area_level_1': location_info.administrative_area_level_1,
                    'administrative_area_level_2': location_info.administrative_area_level_2,
                    'administrative_area_level_3': location_info.administrative_area_level_3,
                    'locality': location_info.locality,
                    'sublocality': location_info.sublocality,
                    'route': location_info.route,
                    'street_number': location_info.street_number,
                    'postal_code': location_info.postal_code,
                    'place_types': location_info.place_types
                }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"缓存已导出到: {output_file}")
        except Exception as e:
            self.logger.error(f"导出缓存失败: {e}")
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        self.logger.info("缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.cache),
            'cache_file': str(self.cache_file),
            'cache_file_exists': self.cache_file.exists(),
            'request_count': self.request_count
        }


def main():
    """示例用法"""
    # 初始化服务（请替换为您的API密钥）
    service = GeocodingService(api_key="AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI")
    
    # 示例坐标（北京天安门）
    test_coordinates = [
        (39.9042, 116.4074),  # 天安门
        (39.9152, 116.3974),  # 中南海附近
        (25.036778523385756, 121.53847461373061),  # 王府井
    ]
    
    print("🌍 地理位置反查服务示例")
    print("=" * 50)
    
    # 单个查询示例
    lat, lon = test_coordinates[2]
    print(f"\n📍 查询坐标: ({lat}, {lon})")
    
    location_info = service.reverse_geocode(lat, lon)
    if location_info:
        print(f"完整地址: {location_info.formatted_address}")
        print(f"摘要地址: {service.get_location_summary(location_info)}")
        print(f"国家: {location_info.country}")
        print(f"省/直辖市: {location_info.administrative_area_level_1}")
        print(f"市: {location_info.administrative_area_level_2}")
        print(f"区: {location_info.administrative_area_level_3}")
    else:
        print("❌ 查询失败，请检查API密钥设置")
    
    # 缓存统计
    stats = service.get_cache_stats()
    print(f"\n📊 缓存统计: {stats}")


if __name__ == "__main__":
    main()
