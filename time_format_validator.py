#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间格式验证工具
确保所有时间处理都使用统一的格式: 2024-01-15 17:27:46
"""

import pandas as pd
import datetime
from typing import List, Union

# 标准时间格式
STANDARD_TIME_FORMAT = '%Y-%m-%d %H:%M:%S'

def validate_csv_time_format(csv_file: str) -> bool:
    """
    验证CSV文件中的时间格式是否符合标准
    
    Args:
        csv_file: CSV文件路径
        
    Returns:
        bool: 是否符合标准格式
    """
    try:
        df = pd.read_csv(csv_file)
        
        if 'timestamp' not in df.columns:
            print("❌ CSV文件缺少timestamp列")
            return False
        
        # 检查每个时间戳
        for idx, timestamp in enumerate(df['timestamp']):
            try:
                # 尝试解析时间戳
                parsed_time = datetime.datetime.strptime(str(timestamp), STANDARD_TIME_FORMAT)
                # 验证格式化后是否一致
                formatted_time = parsed_time.strftime(STANDARD_TIME_FORMAT)
                if str(timestamp) != formatted_time:
                    print(f"❌ 第{idx+1}行时间格式不标准: {timestamp}")
                    return False
            except ValueError as e:
                print(f"❌ 第{idx+1}行时间格式错误: {timestamp} - {e}")
                return False
        
        print(f"✅ CSV文件时间格式验证通过，共{len(df)}条记录")
        print(f"📅 时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def format_timestamp_from_unix(unix_timestamp: int) -> str:
    """
    将Unix时间戳转换为标准格式
    
    Args:
        unix_timestamp: Unix时间戳（秒）
        
    Returns:
        str: 标准格式的时间字符串
    """
    dt = datetime.datetime.fromtimestamp(unix_timestamp)
    return dt.strftime(STANDARD_TIME_FORMAT)

def parse_timestamp_to_unix(timestamp_str: str) -> int:
    """
    将标准格式时间字符串转换为Unix时间戳
    
    Args:
        timestamp_str: 标准格式的时间字符串
        
    Returns:
        int: Unix时间戳（秒）
    """
    dt = datetime.datetime.strptime(timestamp_str, STANDARD_TIME_FORMAT)
    return int(dt.timestamp())

def generate_sample_timestamps(start_time: str, count: int, interval_minutes: int = 60) -> List[str]:
    """
    生成示例时间戳序列
    
    Args:
        start_time: 开始时间（标准格式）
        count: 生成数量
        interval_minutes: 间隔分钟数
        
    Returns:
        List[str]: 时间戳列表
    """
    start_dt = datetime.datetime.strptime(start_time, STANDARD_TIME_FORMAT)
    timestamps = []
    
    for i in range(count):
        current_dt = start_dt + datetime.timedelta(minutes=i * interval_minutes)
        timestamps.append(current_dt.strftime(STANDARD_TIME_FORMAT))
    
    return timestamps

def validate_time_format_consistency():
    """验证项目中时间格式的一致性"""
    print("🕐 验证时间格式一致性")
    print("=" * 50)
    
    # 测试CSV文件
    csv_files = [
        "test_trajectory.csv",
        "test_trajectory_extended.csv"
    ]
    
    for csv_file in csv_files:
        print(f"\n📄 验证文件: {csv_file}")
        try:
            validate_csv_time_format(csv_file)
        except FileNotFoundError:
            print(f"⚠️ 文件不存在: {csv_file}")
    
    # 测试时间转换
    print(f"\n🔄 测试时间转换功能")
    test_timestamp = "2024-01-15 17:27:46"
    unix_time = parse_timestamp_to_unix(test_timestamp)
    converted_back = format_timestamp_from_unix(unix_time)
    
    print(f"原始时间: {test_timestamp}")
    print(f"Unix时间戳: {unix_time}")
    print(f"转换回来: {converted_back}")
    print(f"转换一致性: {'✅ 通过' if test_timestamp == converted_back else '❌ 失败'}")
    
    # 生成示例时间戳
    print(f"\n📝 生成示例时间戳")
    sample_timestamps = generate_sample_timestamps("2024-01-15 09:00:00", 5, 120)
    for i, ts in enumerate(sample_timestamps):
        print(f"  {i+1}. {ts}")

if __name__ == "__main__":
    validate_time_format_consistency()
