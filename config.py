from dataclasses import dataclass
from typing import Optional, Tuple, List

# API 配置
GOOGLE_MAP_API_KEY = 'AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI'
AI_BASE_URL = 'https://integrate.api.nvidia.com/v1/chat/completions'
AI_API = '**********************************************************************'
# AI_MODEL = 'qwen/qwen3-coder-480b-a35b-instruct'
AI_MODEL = 'moonshotai/kimi-k2-instruct'
AI_PROMPT = ""

# 位置分析配置
@dataclass
class Config:
    """配置类，集中管理所有配置参数"""
    # HDBSCAN聚类参数
    DEFAULT_MIN_CLUSTER_SIZE: int = 5  # 默认最小聚类大小
    DEFAULT_CLUSTER_SELECTION_EPSILON: Optional[float] = None  # 默认聚类选择阈值（米）
    STAY_BREAK_MINUTES: int = 30  # 停留间隔阈值（分钟）
    MIN_STAY_HOURS: float = 0.5  # 最小停留时间（小时）
    HOME_SCORE_THRESHOLD: float = 0.8  # 家庭地点识别阈值
    WORK_SCORE_THRESHOLD: float = 0.7  # 工作地点识别阈值
    OTHER_IMPORTANT_THRESHOLD: float = 0.5  # 其他重要地点识别阈值
    MIN_OTHER_VISITS: int = 10  # 其他重要地点最小访问次数
    MAX_OTHER_LOCATIONS: int = 5  # 最大其他重要地点数量
    EARTH_RADIUS_KM: float = 6371.0  # 地球半径（公里）

    # 可视化配置
    FIGURE_SIZE: Tuple[int, int] = (20, 15)
    DPI: int = 300

    # 日志配置
    LOG_FORMAT: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_LEVEL: int = 20  # logging.INFO

# 默认配置实例
DEFAULT_CONFIG = Config()