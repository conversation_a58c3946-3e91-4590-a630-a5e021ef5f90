#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APP定位数据分析器 - 识别家庭地点和工作地点
专门处理手机APP产生的定位数据
"""

import os
import sys
import json
import requests
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config, DEFAULT_CONFIG, GOOGLE_MAP_API_KEY, AI_BASE_URL, AI_API, AI_MODEL


@dataclass
class LocationResult:
    """位置分析结果"""
    home_coordinates: Optional[List[float]] = None  # [纬度, 经度]
    work_coordinates: Optional[List[float]] = None  # [纬度, 经度]
    confidence: float = 0.0
    notes: str = ""


class LocationAnalyzer:
    """APP定位数据分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化分析器
        
        Args:
            config: 配置字典，包含:
                - api_url: API端点URL
                - api_key: API密钥
                - model: 模型名称
                - headers: 请求头 (可选)
                - timeout: 超时时间 (可选，默认30秒)
        """
        self.api_url = config['api_url']
        self.api_key = config['api_key']
        self.model = config['model']
        self.timeout = config.get("timeout", 300)
        
        # 设置请求头
        self.headers = config.get("headers", {})
        if not self.headers:
            self.headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
    
    def _build_prompt(self, location_data: Union[Dict, List, str]) -> str:
        """构建分析提示词 - 专门针对APP定位数据"""
        return f"""
你是专业的位置数据分析师，请分析手机APP收集的GPS定位数据，识别用户的家庭地点和工作地点。

APP定位数据（经纬度坐标）：
{json.dumps(location_data, ensure_ascii=False, indent=2) if isinstance(location_data, (dict, list)) else str(location_data)}

分析规则：
1. 家庭地点特征：
   - 夜间时段（22:00-06:00）停留时间长
   - 周末停留时间长
   - 每天都会回到的坐标点
   
2. 工作地点特征：
   - 工作日白天（09:00-18:00）停留时间长
   - 周末很少出现
   - 工作日规律性强

3. 坐标聚类：
   - 相近的坐标点（100米内）视为同一地点
   - 重点分析高频出现的坐标区域
   - 忽略停留时间少于30分钟的记录

请返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null, 
    "confidence": 0.85,
    "notes": "基于时间模式和坐标聚类的分析依据"
}}
"""
    
    def _build_request(self, prompt: str) -> Dict:
        """构建API请求"""
        return {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "你是专业的APP定位数据分析师。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 800
        }
    
    def _extract_response(self, response_data: Dict) -> str:
        """提取响应内容"""
        # 尝试常见的响应格式
        if "choices" in response_data and response_data["choices"]:
            return response_data["choices"][0]["message"]["content"]
        elif "content" in response_data:
            if isinstance(response_data["content"], list):
                return response_data["content"][0]["text"]
            else:
                return response_data["content"]
        elif "result" in response_data:
            return response_data["result"]
        elif "output" in response_data and "text" in response_data["output"]:
            return response_data["output"]["text"]
        else:
            return str(response_data)
    
    def analyze(self, location_data: Union[Dict, List, str]) -> LocationResult:
        """分析APP定位数据"""
        try:
            # 构建请求
            prompt = self._build_prompt(location_data)
            payload = self._build_request(prompt)
            
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            print("response:", response.json())
            # 解析响应
            content = self._extract_response(response.json())
            
            # 提取JSON结果
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                result_data = json.loads(json_match.group())
                return LocationResult(
                    home_coordinates=result_data.get("home_coordinates"),
                    work_coordinates=result_data.get("work_coordinates"),
                    confidence=float(result_data.get("confidence", 0.0)),
                    notes=result_data.get("notes", "")
                )
            else:
                return LocationResult(notes=content, confidence=0.0)
                
        except Exception as e:
            return LocationResult(notes=f"分析失败: {e}", confidence=0.0)
    
    def batch_analyze(self, data_list: List, delay: float = 1.0) -> List[LocationResult]:
        """批量分析多个用户的定位数据"""
        results = []
        for i, data in enumerate(data_list):
            print(f"分析第 {i+1}/{len(data_list)} 个用户...")
            result = self.analyze(data)
            results.append(result)
            
            if i < len(data_list) - 1:
                time.sleep(delay)
        
        return results


# 使用示例
if __name__ == "__main__":
    # APP定位数据示例（只有坐标，无地址）
    app_location_data = {
        "user_locations": [
            {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "timestamp": "2024-01-15 09:30:00",
                "stay_duration": 480,  # 分钟
                "day_of_week": "Monday"
            },
            {
                "latitude": 39.9842,
                "longitude": 116.3064,
                "timestamp": "2024-01-15 20:00:00", 
                "stay_duration": 600,
                "day_of_week": "Monday"
            },
            {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "timestamp": "2024-01-15 09:30:00",
                "stay_duration": 480,  # 分钟
                "day_of_week": "Tuesday"
            },
            {
                "latitude": 39.9842,
                "longitude": 116.3064,
                "timestamp": "2024-01-15 20:00:00", 
                "stay_duration": 600,
                "day_of_week": "Tuesday"
            },{
                "latitude": 39.9042,
                "longitude": 116.4074,
                "timestamp": "2024-01-15 09:30:00",
                "stay_duration": 480,  # 分钟
                "day_of_week": "Wednesday"
            },
            {
                "latitude": 39.9842,
                "longitude": 116.3064,
                "timestamp": "2024-01-15 20:00:00", 
                "stay_duration": 600,
                "day_of_week": "Wednesday"
            },
            {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "timestamp": "2024-01-15 09:30:00",
                "stay_duration": 480,  # 分钟
                "day_of_week": "Thursday"
            },
            {
                "latitude": 39.9842,
                "longitude": 116.3064,
                "timestamp": "2024-01-15 20:00:00", 
                "stay_duration": 600,
                "day_of_week": "Monday"
            }
            # 更多定位记录...
        ],
        "analysis_period": "2024-01-01 to 2024-01-31",
        "total_records": 850
    }
    
    # 初始化分析器
    config = {
        "api_url": AI_BASE_URL,
        "api_key": AI_API,
        "model": AI_MODEL
    }
    analyzer = LocationAnalyzer(config)
    
    # 分析定位数据
    result = analyzer.analyze(app_location_data)
    
    print("分析结果:", result)
    print(f"家庭坐标: {result.home_coordinates}")
    print(f"工作坐标: {result.work_coordinates}")
    print(f"置信度: {result.confidence}")
    print(f"说明: {result.notes}")