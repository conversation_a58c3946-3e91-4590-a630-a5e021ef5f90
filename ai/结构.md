# APP定位数据分析器

## 功能说明

分析手机APP收集的定位数据，自动识别用户的**家庭地点**和**工作地点**。

## 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 基本使用
```python
from location_analyzer_simple import LocationAnalyzer

# 配置AI服务
config = {
    "api_url": "https://api.openai.com/v1/chat/completions",
    "api_key": "api-key",
    "model": "gpt-3.5-turbo"
}

# 初始化分析器
analyzer = LocationAnalyzer(config)

# APP定位数据
location_data = {
    "user_locations": [
        {
            "latitude": 39.9042,
            "longitude": 116.4074,
            "address": "北京市朝阳区建国门外大街1号",
            "timestamp": "2024-01-15 09:30:00",
            "stay_duration": 480,  # 停留时长(分钟)
            "day_of_week": "Monday"
        },
        {
            "latitude": 39.9842,
            "longitude": 116.3064, 
            "address": "北京市海淀区中关村大街27号",
            "timestamp": "2024-01-15 20:00:00",
            "stay_duration": 600,
            "day_of_week": "Monday"
        }
        # 更多定位记录...
    ]
}

# 执行分析
result = analyzer.analyze(location_data)

# 查看结果
print(f"家庭地点: {result.home_location}")
print(f"工作地点: {result.work_location}")
print(f"置信度: {result.confidence}")
```

## 支持的AI服务商: 大多数支持OpenAI API

## 数据格式

### 标准格式（推荐）
```json
{
    "user_locations": [
        {
            "latitude": 39.9042,
            "longitude": 116.4074,
            "address": "具体地址",
            "timestamp": "2024-01-15 09:30:00",
            "stay_duration": 480,
            "day_of_week": "Monday"
        }
    ],
    "analysis_period": "数据时间范围",
    "total_records": 850
}
```

### 简化格式
```json
[
    {
        "location": "北京市朝阳区建国门外大街1号",
        "time": "2024-01-15 09:30:00", 
        "duration": 480,
        "weekday": true
    }
]
```

### 聚类后格式
```json
{
    "cluster_1": {
        "center_location": "北京市朝阳区建国门外大街1号",
        "visit_times": ["09:00-18:00"] * 22,
        "avg_duration": 8.5,
        "frequency": 22,
        "weekday_ratio": 0.95
    },
    "cluster_2": {
        "center_location": "北京市海淀区中关村大街27号",
        "visit_times": ["19:00-07:00"] * 28,
        "avg_duration": 12.0,
        "frequency": 28,
        "weekend_ratio": 0.90
    }
}
```

## 分析结果

```python
@dataclass
class LocationResult:
    home_location: str      # 家庭地点地址
    work_location: str      # 工作地点地址  
    confidence: float       # 置信度 (0-1)
    notes: str             # 分析说明
```

### 结果处理
```python
result = analyzer.analyze(data)

if result.confidence > 0.7:
    print("✅ 高置信度结果")
    print(f"🏠 家: {result.home_location}")
    print(f"🏢 工作: {result.work_location}")
elif result.confidence > 0.5:
    print("⚠️ 中等置信度，建议人工确认")
else:
    print("❌ 低置信度，数据可能不足")
    print(f"原因: {result.notes}")
```

## 批量处理

```python
# 多个用户的数据
users_data = [user1_data, user2_data, user3_data]

# 批量分析
results = analyzer.batch_analyze(users_data, delay=1.0)

# 处理结果
for i, result in enumerate(results):
    print(f"用户{i+1}: 家={result.home_location}, 工作={result.work_location}")
```


## 错误处理

```python
try:
    result = analyzer.analyze(location_data)
    if result.confidence > 0.5:
        # 处理成功结果
        process_result(result)
    else:
        # 处理低置信度结果
        handle_low_confidence(result)
        
except Exception as e:
    print(f"分析失败: {e}")
    # 检查API密钥、网络连接等
```

## 常见问题

**Q: 需要多少定位数据？**
A: 建议至少1个月的数据，包含工作日和周末的记录。

**Q: 分析不出结果怎么办？**
A: 检查数据是否包含时间信息和停留时长，确保有足够的样本量。

**Q: 如何提高准确性？**
A: 提供更多的时间维度信息，如具体时间、星期几、停留时长等。

**Q: 支持实时分析吗？**
A: 支持，但建议积累一定量的数据后再分析，效果更好。

## 成本参考

- **GPT-3.5-turbo**: ~$0.002/次分析
- **Claude-3-sonnet**: ~$0.015/次分析  
- **国产模型**: 通常更便宜

建议根据精度要求和成本预算选择合适的模型。