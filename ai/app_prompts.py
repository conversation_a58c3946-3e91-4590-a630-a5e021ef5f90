#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APP定位数据专用提示词模板
针对手机APP收集的定位数据优化
"""

import json
from typing import Union, Dict, List


class AppLocationPrompts:
    """APP定位数据分析提示词"""
    
    @staticmethod
    def gps_data_prompt(location_data: Union[Dict, List]) -> str:
        """原始GPS定位数据分析提示词"""
        return f"""
你是专业的手机定位数据分析师，请分析APP收集的GPS定位数据，识别用户的家和工作地点。

GPS定位数据：
{json.dumps(location_data, ensure_ascii=False, indent=2) if isinstance(location_data, (dict, list)) else str(location_data)}

分析标准：
1. 家庭地点判断：
   - 夜间22:00-06:00长时间停留
   - 周末停留时间长
   - 每天最后回到的地点

2. 工作地点判断：
   - 工作日09:00-18:00长时间停留  
   - 周末很少出现
   - 停留时间规律（通常6-10小时）

3. 数据质量要求：
   - 如果停留时间少于30分钟，可能是路过
   - 如果某地点出现次数少于5次，置信度较低
   - 优先分析高频、长时间停留的地点

返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null,
    "confidence": 0.85,
    "notes": "分析依据"
}}
"""

    @staticmethod
    def clustered_data_prompt(cluster_data: Union[Dict, List]) -> str:
        """聚类后定位数据分析提示词"""
        return f"""
你是定位数据分析专家，请分析已聚类的APP定位数据，识别家庭和工作地点。

聚类定位数据：
{json.dumps(cluster_data, ensure_ascii=False, indent=2) if isinstance(cluster_data, (dict, list)) else str(cluster_data)}

聚类分析规则：
1. 家庭地点特征：
   - 夜间和周末访问频率高
   - 平均停留时间长（>8小时）
   - 访问规律性强（几乎每天）

2. 工作地点特征：
   - 工作日白天访问频率高
   - 停留时间集中在6-10小时
   - 周末访问频率低

3. 其他地点：
   - 偶尔访问的地点（购物、娱乐等）
   - 停留时间短（<4小时）
   - 访问不规律

请返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null, 
    "confidence": 0.85,
    "notes": "基于聚类特征的分析说明"
}}
"""

    @staticmethod
    def simple_format_prompt(simple_data: Union[Dict, List]) -> str:
        """简化格式数据分析提示词"""
        return f"""
请分析简化的APP定位数据，识别用户的居住地和工作地。

定位数据：
{json.dumps(simple_data, ensure_ascii=False, indent=2) if isinstance(simple_data, (dict, list)) else str(simple_data)}

分析要点：
1. 重点关注时间模式描述
2. 停留时长是重要判断依据
3. 访问频率反映地点重要性
4. 地点类型提供辅助信息

判断逻辑：
- 居住地：夜间停留、周末停留、长时间停留
- 工作地：工作日白天停留、规律性强、商业区
- 如果信息不足，请明确说明

返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null,
    "confidence": 0.75,
    "notes": "分析过程说明"
}}
"""

    @staticmethod
    def low_quality_data_prompt(poor_data: Union[Dict, List]) -> str:
        """低质量APP数据分析提示词"""
        return f"""
请分析质量较差的APP定位数据，尽可能识别生活地点。

定位数据：
{json.dumps(poor_data, ensure_ascii=False, indent=2) if isinstance(poor_data, (dict, list)) else str(poor_data)}

保守分析策略：
1. 只在有明确证据时做判断
2. 缺少时间信息时，基于地点类型推测
3. 数据量不足时，降低置信度
4. 明确说明数据局限性

分析原则：
- 住宅区地址更可能是家
- 商业办公区更可能是工作地点
- 访问频率高的地点更重要
- 不确定时返回null

返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null,
    "confidence": 0.3,
    "notes": "数据质量说明和分析局限性"
}}
"""

    @staticmethod
    def real_time_prompt(recent_data: Union[Dict, List]) -> str:
        """实时/近期数据分析提示词"""
        return f"""
请分析近期的APP定位数据，识别当前的生活模式。

近期定位数据：
{json.dumps(recent_data, ensure_ascii=False, indent=2) if isinstance(recent_data, (dict, list)) else str(recent_data)}

实时分析要点：
1. 重点关注最近1-2周的模式
2. 考虑可能的生活变化（搬家、换工作等）
3. 新出现的高频地点可能是新的家/工作地
4. 旧地点访问减少可能表示变化

分析策略：
- 优先分析最新的规律性模式
- 对比新旧地点的访问频率
- 考虑季节性或临时性变化
- 标注分析的时效性

返回JSON格式：
{{
    "home_coordinates": [纬度, 经度] 或null,
    "work_coordinates": [纬度, 经度] 或null,
    "confidence": 0.70,
    "notes": "基于近期数据的分析，可能存在变化"
}}
"""


def select_prompt_by_data_type(data: Union[Dict, List]) -> str:
    """根据数据类型自动选择合适的提示词"""
    
    data_str = str(data).lower()
    
    # 检查数据特征
    has_timestamp = any(keyword in data_str for keyword in ['timestamp', 'time', '2024', '2023'])
    has_coordinates = any(keyword in data_str for keyword in ['lat', 'lng', 'latitude', 'longitude'])
    has_duration = any(keyword in data_str for keyword in ['duration', 'stay', 'hours', 'minutes'])
    has_cluster = any(keyword in data_str for keyword in ['cluster', 'center', 'visit_pattern'])
    
    # 数据量评估
    data_size = len(str(data))
    
    if has_cluster and has_duration:
        # 聚类后的数据
        return AppLocationPrompts.clustered_data_prompt(data)
    elif has_coordinates and has_timestamp and has_duration:
        # 原始GPS数据
        return AppLocationPrompts.gps_data_prompt(data)
    elif data_size < 500:
        # 数据量较小，可能质量不高
        return AppLocationPrompts.low_quality_data_prompt(data)
    else:
        # 简化格式数据
        return AppLocationPrompts.simple_format_prompt(data)


# 使用示例
if __name__ == "__main__":
    print("📱 APP定位数据提示词模板")
    print("=" * 40)
    
    # 示例数据
    sample_gps = {
        "locations": [
            {"lat": 39.9042, "lng": 116.4074, "timestamp": "2024-01-15 09:30:00", "duration": 480}
        ]
    }
    
    sample_cluster = {
        "cluster_1": {
            "center": "北京市朝阳区",
            "visit_pattern": {"weekday": 22, "weekend": 2},
            "avg_duration": 8.5
        }
    }
    
    sample_simple = [
        {"location": "某办公楼", "pattern": "工作日白天", "duration": "8小时"}
    ]
    
    # 自动选择提示词
    print("🔍 自动提示词选择:")
    print(f"GPS数据 -> {type(select_prompt_by_data_type(sample_gps)).__name__}")
    print(f"聚类数据 -> {type(select_prompt_by_data_type(sample_cluster)).__name__}")
    print(f"简单数据 -> {type(select_prompt_by_data_type(sample_simple)).__name__}")
    
    print("\n💡 使用建议:")
    print("1. 原始GPS数据使用 gps_data_prompt")
    print("2. 聚类后数据使用 clustered_data_prompt") 
    print("3. 简化数据使用 simple_format_prompt")
    print("4. 质量差的数据使用 low_quality_data_prompt")
    print("5. 或使用 select_prompt_by_data_type 自动选择")