# 使用官方Python运行时作为基础镜像
FROM registry.eos-harbor.com/build/python:3.13-trixie

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/tmp/uv-cache 
# 更换为中国源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装uv（使用中国镜像）
RUN mkdir -p /root/.local/bin
RUN curl -LsSf http://*************:9000/cs-tools/uv -o /root/.local/bin/uv && \
    chmod +x /root/.local/bin/uv
RUN curl -LsSf http://*************:9000/cs-tools/uvx -o /root/.local/bin/uvx && \
    chmod +x /root/.local/bin/uvx
ENV PATH="/root/.local/bin:$PATH"

# 配置uv使用中国源
ENV UV_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/
ENV UV_EXTRA_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/

# 复制依赖文件
COPY requirements.txt ./

# 创建虚拟环境并安装依赖
RUN uv venv /app/.venv
ENV VIRTUAL_ENV=/app/.venv
ENV PATH="/app/.venv/bin:$PATH"

# 使用uv安装依赖
RUN uv pip install -r requirements.txt

# 复制应用代码
COPY ./*.py .

# 清理uv缓存
RUN rm -rf /tmp/uv-cache

# 暴露gRPC服务端口
EXPOSE 50051

# 运行服务器
CMD ["python", "server.py"]