import grpc
from concurrent import futures
import time
import requests
import pandas as pd
import os
import sys
from typing import Dict, List, Any
import tempfile

# import the generated classes
from pb2 import analyzer_pb2
from pb2 import analyzer_pb2_grpc
from opencc import OpenCC

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config import DEFAULT_CONFIG, GOOGLE_MAP_API_KEY, AI_BASE_URL, AI_API, AI_MODEL
from ai.location_analyzer_ai import LocationAnalyzer as AILocationAnalyzer, LocationResult
from analyze.location_analyzer_enhanced import EnhancedLocationAnalyzer
from analyze.geocoding_openstreet import OpenStreetGeocodingService


class IntegratedLocationService:
    """整合的位置分析服务"""

    def __init__(self):
        """初始化整合服务"""
        # 初始化AI分析器
        ai_config = {
            "api_url": AI_BASE_URL,
            "api_key": AI_API,
            "model": AI_MODEL
        }
        self.ai_analyzer = AILocationAnalyzer(ai_config)

        # 初始化增强分析器
        self.enhanced_analyzer = EnhancedLocationAnalyzer(
            google_api_key=GOOGLE_MAP_API_KEY,
            config=DEFAULT_CONFIG
        )

        # 初始化地理编码服务
        self.geocoding_service = OpenStreetGeocodingService()

    def analyze_trajectory_points(self, points: List[analyzer_pb2.TrajectoryPoint],
                                csv_path: str = None, use_ai: bool = False) -> Dict[str, Any]:
        """
        分析轨迹点数据

        Args:
            points: 轨迹点列表
            csv_path: CSV文件路径（可选）
            use_ai: 是否使用AI分析

        Returns:
            Dict: 分析结果
        """
        try:
            # 如果有CSV文件，优先使用CSV数据
            if csv_path and os.path.exists(csv_path):
                return self._analyze_from_csv(csv_path, use_ai)

            # 否则使用轨迹点数据
            if not points:
                return {"success": False, "error": "没有提供有效的轨迹数据"}

            return self._analyze_from_points(points, use_ai)

        except Exception as e:
            return {"success": False, "error": f"分析失败: {str(e)}"}

    def _analyze_from_csv(self, csv_path: str, use_ai: bool) -> Dict[str, Any]:
        """从CSV文件分析"""
        if use_ai:
            # 使用AI分析：读取CSV并转换为AI分析器需要的格式
            try:
                df = pd.read_csv(csv_path)
                location_data = self._convert_csv_to_ai_format(df)
                ai_result = self.ai_analyzer.analyze(location_data)
                return self._convert_ai_result_to_response(ai_result)
            except Exception as e:
                return {"success": False, "error": f"AI分析失败: {str(e)}"}
        else:
            # 使用增强分析器
            try:
                result = self.enhanced_analyzer.analyze_simple(csv_path)
                return result
            except Exception as e:
                return {"success": False, "error": f"增强分析失败: {str(e)}"}

    def _analyze_from_points(self, points: List[analyzer_pb2.TrajectoryPoint],
                           use_ai: bool) -> Dict[str, Any]:
        """从轨迹点分析"""
        try:
            # 创建临时CSV文件
            temp_csv = self._create_temp_csv_from_points(points)

            if use_ai:
                # 转换为AI分析器格式
                location_data = self._convert_points_to_ai_format(points)
                ai_result = self.ai_analyzer.analyze(location_data)
                return self._convert_ai_result_to_response(ai_result)
            else:
                # 使用增强分析器
                result = self.enhanced_analyzer.analyze_simple(temp_csv)
                return result

        except Exception as e:
            return {"success": False, "error": f"轨迹点分析失败: {str(e)}"}
        finally:
            # 清理临时文件
            if 'temp_csv' in locals() and os.path.exists(temp_csv):
                os.unlink(temp_csv)

    def _create_temp_csv_from_points(self, points: List[analyzer_pb2.TrajectoryPoint]) -> str:
        """从轨迹点创建临时CSV文件"""
        import datetime

        data = []
        for point in points:
            # 转换时间戳为datetime
            dt = datetime.datetime.fromtimestamp(point.ts_sec)
            data.append({
                'latitude': point.lat,
                'longitude': point.lng,
                'timestamp': dt.strftime('%Y-%m-%d %H:%M:%S')
            })

        df = pd.DataFrame(data)

        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        df.to_csv(temp_file.name, index=False)
        temp_file.close()

        return temp_file.name

    def _convert_points_to_ai_format(self, points: List[analyzer_pb2.TrajectoryPoint]) -> Dict:
        """将轨迹点转换为AI分析器格式"""
        import datetime

        user_locations = []
        for point in points:
            dt = datetime.datetime.fromtimestamp(point.ts_sec)
            user_locations.append({
                "latitude": point.lat,
                "longitude": point.lng,
                "timestamp": dt.strftime('%Y-%m-%d %H:%M:%S'),
                "day_of_week": dt.strftime('%A')
            })

        return {
            "user_locations": user_locations,
            "total_records": len(user_locations)
        }

    def _convert_csv_to_ai_format(self, df: pd.DataFrame) -> Dict:
        """将CSV数据转换为AI分析器格式"""
        user_locations = []
        for _, row in df.iterrows():
            user_locations.append({
                "latitude": row['latitude'],
                "longitude": row['longitude'],
                "timestamp": str(row['timestamp']),
                "day_of_week": pd.to_datetime(row['timestamp']).strftime('%A') if 'timestamp' in row else 'Unknown'
            })

        return {
            "user_locations": user_locations,
            "total_records": len(user_locations)
        }

    def _convert_ai_result_to_response(self, ai_result: LocationResult) -> Dict[str, Any]:
        """将AI分析结果转换为统一格式"""
        result = {
            "success": ai_result.confidence > 0,
            "home": None,
            "work": None,
            "other_important": [],
            "confidence": ai_result.confidence,
            "notes": ai_result.notes
        }

        if ai_result.home_coordinates:
            result["home"] = {
                "coordinates": ai_result.home_coordinates,
                "address": "AI分析识别的家庭地址",
                "confidence": ai_result.confidence
            }

        if ai_result.work_coordinates:
            result["work"] = {
                "coordinates": ai_result.work_coordinates,
                "address": "AI分析识别的工作地址",
                "confidence": ai_result.confidence
            }

        return result


# create a class to define the server functions, derived from
# analyzer_pb2_grpc.LocationInferServiceServicer
class LocationInferServiceServicer(analyzer_pb2_grpc.LocationInferServiceServicer):

    def __init__(self):
        """初始化服务"""
        super().__init__()
        self.location_service = IntegratedLocationService()

    # Infer is the implementation of the rpc method
    def Infer(self, request, context):
        print("Received Infer request")
        print(f"轨迹点数量: {len(request.points)}")
        print(f"CSV文件路径: {request.csv}")
        print(f"使用AI分析: {request.ai}")

        try:
            # 使用整合的位置分析服务
            analysis_result = self.location_service.analyze_trajectory_points(
                points=list(request.points),
                csv_path=request.csv if request.csv else None,
                use_ai=request.ai
            )

            if not analysis_result.get("success", False):
                print(f"分析失败: {analysis_result.get('error', '未知错误')}")
                # 返回空结果
                return self._create_empty_response()

            print(f"分析成功，置信度: {analysis_result.get('confidence', 0)}")

            # 转换分析结果为gRPC响应格式
            return self._convert_analysis_to_response(analysis_result)

        except Exception as e:
            print(f"Infer方法执行失败: {str(e)}")
            return self._create_empty_response()

    def _create_empty_response(self):
        """创建空的响应"""
        # 创建一个虚拟的1x1像素PNG图像
        dummy_portrait = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'

        return analyzer_pb2.LocationInferResponse(
            home=[],
            work=[],
            another=[],
            portrait=dummy_portrait
        )

    def _convert_analysis_to_response(self, analysis_result: Dict[str, Any]):
        """将分析结果转换为gRPC响应"""
        home_addresses = []
        work_addresses = []
        other_addresses = []

        # 处理家庭地址
        if analysis_result.get("home"):
            home_info = analysis_result["home"]
            coordinates = home_info.get("coordinates", [0.0, 0.0])

            # 获取地理编码信息
            location_info = None
            if len(coordinates) >= 2:
                location_info = self.location_service.geocoding_service.reverse_geocode(
                    coordinates[0], coordinates[1]
                )

            home_location = self._create_location_from_info(location_info, home_info.get("address", ""))
            home_address = analyzer_pb2.Address(
                location=home_location,
                lat=coordinates[0] if len(coordinates) >= 2 else 0.0,
                lng=coordinates[1] if len(coordinates) >= 2 else 0.0,
                confidence=float(home_info.get("confidence", 0.0))
            )
            home_addresses.append(home_address)

        # 处理工作地址
        if analysis_result.get("work"):
            work_info = analysis_result["work"]
            coordinates = work_info.get("coordinates", [0.0, 0.0])

            # 获取地理编码信息
            location_info = None
            if len(coordinates) >= 2:
                location_info = self.location_service.geocoding_service.reverse_geocode(
                    coordinates[0], coordinates[1]
                )

            work_location = self._create_location_from_info(location_info, work_info.get("address", ""))
            work_address = analyzer_pb2.Address(
                location=work_location,
                lat=coordinates[0] if len(coordinates) >= 2 else 0.0,
                lng=coordinates[1] if len(coordinates) >= 2 else 0.0,
                confidence=float(work_info.get("confidence", 0.0))
            )
            work_addresses.append(work_address)

        # 处理其他重要地点
        for other_info in analysis_result.get("other_important", []):
            coordinates = other_info.get("coordinates", [0.0, 0.0])

            # 获取地理编码信息
            location_info = None
            if len(coordinates) >= 2:
                location_info = self.location_service.geocoding_service.reverse_geocode(
                    coordinates[0], coordinates[1]
                )

            other_location = self._create_location_from_info(location_info, other_info.get("address", ""))
            other_address = analyzer_pb2.Address(
                location=other_location,
                lat=coordinates[0] if len(coordinates) >= 2 else 0.0,
                lng=coordinates[1] if len(coordinates) >= 2 else 0.0,
                confidence=float(other_info.get("confidence", 0.0))
            )
            other_addresses.append(other_address)

        # 创建虚拟头像
        dummy_portrait = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'

        return analyzer_pb2.LocationInferResponse(
            home=home_addresses,
            work=work_addresses,
            another=other_addresses,
            portrait=dummy_portrait
        )

    def _create_location_from_info(self, location_info, fallback_address: str = ""):
        """从地理编码信息创建Location对象"""
        if location_info:
            return analyzer_pb2.Location(
                country=location_info.country or "",
                state=location_info.administrative_area_level_1 or "",
                city=location_info.administrative_area_level_2 or location_info.locality or "",
                county=location_info.administrative_area_level_3 or "",
                town="",
                village=location_info.village or "",
                suburb=location_info.sublocality or location_info.neighborhood or "",
                road=location_info.route or "",
                house_number=location_info.street_number or "",
                postcode=location_info.postal_code or ""
            )
        else:
            # 如果没有地理编码信息，使用fallback地址
            return analyzer_pb2.Location(
                country="",
                state="",
                city="",
                county="",
                town="",
                village="",
                suburb=fallback_address,
                road="",
                house_number="",
                postcode=""
            )

    # GeoCode is the implementation of the rpc method
    def GeoCode(self, request, context):
        print("Received GeoCode request")
        print("point:", request.lat, request.lng, request.ts_sec)
              
        # 构建请求参数
        params = {
            'lat': request.lat,
            'lon': request.lng,
            'format': 'json',
            'addressdetails': '1',
            'accept-language': 'zh',
            'zoom': '18'
        }
        
        # 构建完整URL
        base_url = "https://nominatim.openstreetmap.org/reverse"
         
        
        try:
            # Nominatim要求限制请求频率
            time.sleep(1)
            
           
            session = requests.Session()
            session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            response = session.get(base_url, params=params, timeout=15)
            response.raise_for_status()
            # data = response.json()
            if response.status_code == 200:
                location = analyzer_pb2.Location()
                data = response.json()
                address = data.get('address', {})
                location.country = address.get('country', '')
                location.state = address.get('state', '')
                location.city = address.get('city', '')
                location.county = address.get('county', '')
                location.town = address.get('town', '')
                location.village = address.get('village', '')
                location.suburb = address.get('suburb', '')
                location.road = address.get('road', '')
                location.house_number = address.get('house_number', '')
                location.postcode = address.get('postcode', '')
                return location
            else:
                print(f"API请求失败: {response.status_code}")
        except Exception as e2:
            print(f"备用配置也失败: {e2}")
      
        # 如果API请求失败，使用默认地址
        location = analyzer_pb2.Location()
        
        return location

    # MapMark is the implementation of the rpc method  
    def MapMark(self, request, context):
        print("Received MapMark request")
        print("points count:", len(request.points))
        for point in request.points:
            print("point:", point.lat, point.lng, point.ts_sec)
        
        # todo 这个地方生成地图标注图像， 待完成
        # Create a dummy image bytes (a simple 1x1 pixel PNG)
        dummy_map_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
        
        response = analyzer_pb2.MapMarkResponse(
            image=dummy_map_image
        )
        return response

    # Simplify is the implementation of the rpc method
    def Simplify(self, request, context):
        print("Received Simplify request")
        print("input text:", request.text)
        
        # todo 这个地方实现繁体转简体的逻辑， 待完成
        # 这里只是简单的示例，实际需要使用专门的繁简转换库 
        cc = OpenCC('t2s')  # 繁体转简体（t: Traditional, s: Simplified）
        result = cc.convert(request.text)   
        
        
        response = analyzer_pb2.TextResponse(
            text=repr(result)
        )
        return response

    # Traditionalize is the implementation of the rpc method
    def Traditionalize(self, request, context):
        print("Received Traditionalize request")
        print("input text:", request.text)
        
        # todo 这个地方实现简体转繁体的逻辑， 待完成
        # 这里只是简单的示例，实际需要使用专门的繁简转换库 
        cc = OpenCC('s2t')  # 繁体转简体（t: Traditional, s: Simplified）
        result = cc.convert(request.text)   
        response = analyzer_pb2.TextResponse(
            text=repr(result)
        )
        return response

def serve():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    analyzer_pb2_grpc.add_LocationInferServiceServicer_to_server(
        LocationInferServiceServicer(), server)
    server.add_insecure_port('[::]:50051')
    server.start()
    print("Server started on port 50051.")
    try:
        while True:
            time.sleep(86400)
    except KeyboardInterrupt:
        server.stop(0)

if __name__ == '__main__':
    serve()
