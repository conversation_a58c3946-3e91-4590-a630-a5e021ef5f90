import grpc
from concurrent import futures
import time
import requests

# import the generated classes
from pb2 import analyzer_pb2
from pb2 import analyzer_pb2_grpc
from opencc import OpenCC

# create a class to define the server functions, derived from
# analyzer_pb2_grpc.LocationInferServiceServicer
class LocationInferServiceServicer(analyzer_pb2_grpc.LocationInferServiceServicer):

    # Infer is the implementation of the rpc method
    def Infer(self, request, context):
        print("Received Infer request")
        # Here you would add your logic to process the request
        # For now, we'll return a dummy response.
        print("request:", request.points)
        for point in request.points:
            print("point:", point.lat, point.lng, point.ts_sec)

        print("request:", request.csv)
        print("request:", request.ai)

        # todo 这个地方使用python分析家庭地址和工作地址， 待完成
        home_location = analyzer_pb2.Location(
            country="中国",
            state="北京市",
            city="北京市",
            county="朝阳区",
            town="",
            village="",
            suburb="三里屯街道",
            road="工体北路",
            house_number="123号",
            postcode="100027"
        )
        
        home_address = analyzer_pb2.Address(
            location=home_location,
            lat=34.0522,
            lng=-118.2437,
            confidence=0.9
        )

        work_location = analyzer_pb2.Location(
            country="中国",
            state="北京市", 
            city="北京市",
            county="海淀区",
            town="",
            village="",
            suburb="中关村街道",
            road="中关村大街",
            house_number="456号",
            postcode="100080"
        )

        work_address = analyzer_pb2.Address(
            location=work_location,
            lat=37.7749,
            lng=-122.4194,
            confidence=0.8
        )

        another_location = analyzer_pb2.Location(
            country="中国",
            state="上海市",
            city="上海市", 
            county="浦东新区",
            town="",
            village="",
            suburb="陆家嘴街道",
            road="世纪大道",
            house_number="789号",
            postcode="200120"
        )

        another_address = analyzer_pb2.Address(
            location=another_location,
            lat=40.7128,
            lng=-74.0060,
            confidence=0.7
        )

        # Create a dummy image bytes (a 1x1 pixel PNG)
        dummy_portrait = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'

        response = analyzer_pb2.LocationInferResponse(
            home=[home_address, home_address],
            work=[work_address],
            another=[another_address],
            portrait=dummy_portrait
        )
        return response

    # GeoCode is the implementation of the rpc method
    def GeoCode(self, request, context):
        print("Received GeoCode request")
        print("point:", request.lat, request.lng, request.ts_sec)
              
        # 构建请求参数
        params = {
            'lat': request.lat,
            'lon': request.lng,
            'format': 'json',
            'addressdetails': '1',
            'accept-language': 'zh',
            'zoom': '18'
        }
        
        # 构建完整URL
        base_url = "https://nominatim.openstreetmap.org/reverse"
         
        
        try:
            # Nominatim要求限制请求频率
            time.sleep(1)
            
           
            session = requests.Session()
            session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            response = session.get(base_url, params=params, timeout=15)
            response.raise_for_status()
            # data = response.json()
            if response.status_code == 200:
                location = analyzer_pb2.Location()
                data = response.json()
                address = data.get('address', {})
                location.country = address.get('country', '')
                location.state = address.get('state', '')
                location.city = address.get('city', '')
                location.county = address.get('county', '')
                location.town = address.get('town', '')
                location.village = address.get('village', '')
                location.suburb = address.get('suburb', '')
                location.road = address.get('road', '')
                location.house_number = address.get('house_number', '')
                location.postcode = address.get('postcode', '')
                return location
            else:
                print(f"API请求失败: {response.status_code}")
        except Exception as e2:
            print(f"备用配置也失败: {e2}")
      
        # 如果API请求失败，使用默认地址
        location = analyzer_pb2.Location()
        
        return location

    # MapMark is the implementation of the rpc method  
    def MapMark(self, request, context):
        print("Received MapMark request")
        print("points count:", len(request.points))
        for point in request.points:
            print("point:", point.lat, point.lng, point.ts_sec)
        
        # todo 这个地方生成地图标注图像， 待完成
        # Create a dummy image bytes (a simple 1x1 pixel PNG)
        dummy_map_image = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
        
        response = analyzer_pb2.MapMarkResponse(
            image=dummy_map_image
        )
        return response

    # Simplify is the implementation of the rpc method
    def Simplify(self, request, context):
        print("Received Simplify request")
        print("input text:", request.text)
        
        # todo 这个地方实现繁体转简体的逻辑， 待完成
        # 这里只是简单的示例，实际需要使用专门的繁简转换库 
        cc = OpenCC('t2s')  # 繁体转简体（t: Traditional, s: Simplified）
        result = cc.convert(request.text)   
        
        
        response = analyzer_pb2.TextResponse(
            text=repr(result)
        )
        return response

    # Traditionalize is the implementation of the rpc method
    def Traditionalize(self, request, context):
        print("Received Traditionalize request")
        print("input text:", request.text)
        
        # todo 这个地方实现简体转繁体的逻辑， 待完成
        # 这里只是简单的示例，实际需要使用专门的繁简转换库 
        cc = OpenCC('s2t')  # 繁体转简体（t: Traditional, s: Simplified）
        result = cc.convert(request.text)   
        response = analyzer_pb2.TextResponse(
            text=repr(result)
        )
        return response

def serve():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    analyzer_pb2_grpc.add_LocationInferServiceServicer_to_server(
        LocationInferServiceServicer(), server)
    server.add_insecure_port('[::]:50051')
    server.start()
    print("Server started on port 50051.")
    try:
        while True:
            time.sleep(86400)
    except KeyboardInterrupt:
        server.stop(0)

if __name__ == '__main__':
    serve()
